#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Reports Frame for TNB Management System
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import os
from datetime import datetime
from .styles import Styles

class ReportsFrame(tk.Frame):
    def __init__(self, parent, app):
        """Initialize the reports frame."""
        self.app = app
        self.theme = app.theme
        
        super().__init__(parent, bg=self.theme["bg"])
        
        # Create widgets
        self.create_widgets()
    
    def create_widgets(self):
        """Create the reports frame widgets."""
        # Main container with padding
        main_container = tk.Frame(self, bg=self.theme["bg"], padx=20, pady=20)
        main_container.pack(fill=tk.BOTH, expand=True)
        
        # Title
        title_label = tk.Label(
            main_container,
            text="Génération de Rapports",
            **Styles.get_label_style(self.theme, size="title", weight="bold")
        )
        title_label.pack(anchor=tk.W, pady=(0, 20))
        
        # Report types frame
        report_types_frame = tk.LabelFrame(
            main_container,
            text="Types de Rapports",
            bg=self.theme["bg"],
            fg=self.theme["fg"],
            font=("Arial", 12, "bold"),
            padx=10,
            pady=10
        )
        report_types_frame.pack(fill=tk.X, pady=10)
        
        # Report type selection
        self.report_type_var = tk.StringVar(value="taxpayers")
        
        taxpayers_radio = tk.Radiobutton(
            report_types_frame,
            text="Liste des Contribuables",
            variable=self.report_type_var,
            value="taxpayers",
            bg=self.theme["bg"],
            fg=self.theme["fg"],
            selectcolor=self.theme["bg"],
            activebackground=self.theme["bg"],
            activeforeground=self.theme["fg"]
        )
        taxpayers_radio.grid(row=0, column=0, sticky=tk.W, padx=10, pady=5)
        
        properties_radio = tk.Radiobutton(
            report_types_frame,
            text="Liste des Terrains",
            variable=self.report_type_var,
            value="properties",
            bg=self.theme["bg"],
            fg=self.theme["fg"],
            selectcolor=self.theme["bg"],
            activebackground=self.theme["bg"],
            activeforeground=self.theme["fg"]
        )
        properties_radio.grid(row=0, column=1, sticky=tk.W, padx=10, pady=5)
        
        payments_radio = tk.Radiobutton(
            report_types_frame,
            text="Liste des Paiements",
            variable=self.report_type_var,
            value="payments",
            bg=self.theme["bg"],
            fg=self.theme["fg"],
            selectcolor=self.theme["bg"],
            activebackground=self.theme["bg"],
            activeforeground=self.theme["fg"]
        )
        payments_radio.grid(row=1, column=0, sticky=tk.W, padx=10, pady=5)
        
        summary_radio = tk.Radiobutton(
            report_types_frame,
            text="Rapport de Synthèse",
            variable=self.report_type_var,
            value="summary",
            bg=self.theme["bg"],
            fg=self.theme["fg"],
            selectcolor=self.theme["bg"],
            activebackground=self.theme["bg"],
            activeforeground=self.theme["fg"]
        )
        summary_radio.grid(row=1, column=1, sticky=tk.W, padx=10, pady=5)
        
        # Configure grid
        report_types_frame.grid_columnconfigure(0, weight=1)
        report_types_frame.grid_columnconfigure(1, weight=1)
        
        # Filters frame
        filters_frame = tk.LabelFrame(
            main_container,
            text="Filtres",
            bg=self.theme["bg"],
            fg=self.theme["fg"],
            font=("Arial", 12, "bold"),
            padx=10,
            pady=10
        )
        filters_frame.pack(fill=tk.X, pady=10)
        
        # Date range
        date_frame = tk.Frame(filters_frame, bg=self.theme["bg"])
        date_frame.pack(fill=tk.X, pady=5)
        
        date_label = tk.Label(
            date_frame,
            text="Période:",
            **Styles.get_label_style(self.theme)
        )
        date_label.pack(side=tk.LEFT, padx=5)
        
        # Start date
        start_date_label = tk.Label(
            date_frame,
            text="Du:",
            **Styles.get_label_style(self.theme)
        )
        start_date_label.pack(side=tk.LEFT, padx=5)
        
        self.start_date_entry = tk.Entry(
            date_frame,
            width=10,
            **Styles.get_entry_style(self.theme)
        )
        self.start_date_entry.pack(side=tk.LEFT, padx=5)
        self.start_date_entry.insert(0, "01/01/2023")
        
        # End date
        end_date_label = tk.Label(
            date_frame,
            text="Au:",
            **Styles.get_label_style(self.theme)
        )
        end_date_label.pack(side=tk.LEFT, padx=5)
        
        self.end_date_entry = tk.Entry(
            date_frame,
            width=10,
            **Styles.get_entry_style(self.theme)
        )
        self.end_date_entry.pack(side=tk.LEFT, padx=5)
        current_date = datetime.now().strftime("%d/%m/%Y")
        self.end_date_entry.insert(0, current_date)
        
        # Additional filters based on report type
        self.filters_container = tk.Frame(filters_frame, bg=self.theme["bg"])
        self.filters_container.pack(fill=tk.X, pady=5)
        
        # Output options frame
        output_frame = tk.LabelFrame(
            main_container,
            text="Options de Sortie",
            bg=self.theme["bg"],
            fg=self.theme["fg"],
            font=("Arial", 12, "bold"),
            padx=10,
            pady=10
        )
        output_frame.pack(fill=tk.X, pady=10)
        
        # Output format
        format_frame = tk.Frame(output_frame, bg=self.theme["bg"])
        format_frame.pack(fill=tk.X, pady=5)
        
        format_label = tk.Label(
            format_frame,
            text="Format:",
            **Styles.get_label_style(self.theme)
        )
        format_label.pack(side=tk.LEFT, padx=5)
        
        self.format_var = tk.StringVar(value="pdf")
        
        pdf_radio = tk.Radiobutton(
            format_frame,
            text="PDF",
            variable=self.format_var,
            value="pdf",
            bg=self.theme["bg"],
            fg=self.theme["fg"],
            selectcolor=self.theme["bg"],
            activebackground=self.theme["bg"],
            activeforeground=self.theme["fg"]
        )
        pdf_radio.pack(side=tk.LEFT, padx=5)
        
        excel_radio = tk.Radiobutton(
            format_frame,
            text="Excel",
            variable=self.format_var,
            value="excel",
            bg=self.theme["bg"],
            fg=self.theme["fg"],
            selectcolor=self.theme["bg"],
            activebackground=self.theme["bg"],
            activeforeground=self.theme["fg"]
        )
        excel_radio.pack(side=tk.LEFT, padx=5)
        
        # Output path
        path_frame = tk.Frame(output_frame, bg=self.theme["bg"])
        path_frame.pack(fill=tk.X, pady=5)
        
        path_label = tk.Label(
            path_frame,
            text="Emplacement:",
            **Styles.get_label_style(self.theme)
        )
        path_label.pack(side=tk.LEFT, padx=5)
        
        self.path_entry = tk.Entry(
            path_frame,
            width=40,
            **Styles.get_entry_style(self.theme)
        )
        self.path_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        self.path_entry.insert(0, os.path.join(os.getcwd(), "reports"))
        
        browse_button = tk.Button(
            path_frame,
            text="Parcourir",
            command=self.browse_output_path,
            **Styles.get_button_style(self.theme)
        )
        browse_button.pack(side=tk.LEFT, padx=5)
        
        # Generate button
        button_frame = tk.Frame(main_container, bg=self.theme["bg"])
        button_frame.pack(fill=tk.X, pady=20)
        
        generate_button = tk.Button(
            button_frame,
            text="Générer le Rapport",
            command=self.generate_report,
            **Styles.get_button_style(self.theme, "success")
        )
        generate_button.pack(side=tk.RIGHT, padx=5)
        
        # Preview area
        preview_frame = tk.LabelFrame(
            main_container,
            text="Aperçu",
            bg=self.theme["bg"],
            fg=self.theme["fg"],
            font=("Arial", 12, "bold"),
            padx=10,
            pady=10
        )
        preview_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # Create treeview for preview
        columns = ("col1", "col2", "col3", "col4", "col5")
        self.preview_tree = ttk.Treeview(
            preview_frame,
            columns=columns,
            show="headings",
            height=10
        )
        
        # Define headings (will be updated based on report type)
        for col in columns:
            self.preview_tree.heading(col, text=col.capitalize())
            self.preview_tree.column(col, width=100)
        
        # Add scrollbars
        y_scrollbar = ttk.Scrollbar(preview_frame, orient=tk.VERTICAL, command=self.preview_tree.yview)
        self.preview_tree.configure(yscroll=y_scrollbar.set)
        
        x_scrollbar = ttk.Scrollbar(preview_frame, orient=tk.HORIZONTAL, command=self.preview_tree.xview)
        self.preview_tree.configure(xscroll=x_scrollbar.set)
        
        # Pack treeview and scrollbars
        self.preview_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        y_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        x_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # Bind report type change
        self.report_type_var.trace_add("write", self.on_report_type_changed)
        
        # Initialize with default report type
        self.on_report_type_changed()
    
    def on_report_type_changed(self, *args):
        """Handle report type change event."""
        report_type = self.report_type_var.get()
        
        # Clear existing filters
        for widget in self.filters_container.winfo_children():
            widget.destroy()
        
        # Clear preview
        for item in self.preview_tree.get_children():
            self.preview_tree.delete(item)
        
        # Update preview columns based on report type
        if report_type == "taxpayers":
            columns = ("id", "name", "cin", "fiscal_id", "address")
            self.preview_tree["columns"] = columns
            
            for col in columns:
                self.preview_tree.heading(col, text=col.capitalize())
            
            self.preview_tree.column("id", width=50, anchor=tk.CENTER)
            self.preview_tree.column("name", width=200)
            self.preview_tree.column("cin", width=100)
            self.preview_tree.column("fiscal_id", width=100)
            self.preview_tree.column("address", width=200)
            
            # Add sample data
            for i in range(1, 10):
                self.preview_tree.insert("", tk.END, values=(
                    i,
                    f"Nom Contribuable {i}",
                    f"AB{i}12345",
                    f"F{i}98765",
                    f"Adresse {i}, Ville"
                ))
        
        elif report_type == "properties":
            columns = ("id", "reference", "address", "area", "zone", "category")
            self.preview_tree["columns"] = columns
            
            for col in columns:
                self.preview_tree.heading(col, text=col.capitalize())
            
            self.preview_tree.column("id", width=50, anchor=tk.CENTER)
            self.preview_tree.column("reference", width=100)
            self.preview_tree.column("address", width=200)
            self.preview_tree.column("area", width=80, anchor=tk.E)
            self.preview_tree.column("zone", width=100)
            self.preview_tree.column("category", width=100)
            
            # Add sample data
            for i in range(1, 10):
                self.preview_tree.insert("", tk.END, values=(
                    i,
                    f"REF-{i}",
                    f"Adresse Terrain {i}, Ville",
                    f"{i * 100}",
                    f"Zone {i}",
                    f"Catégorie {i % 3 + 1}"
                ))
        
        elif report_type == "payments":
            columns = ("id", "taxpayer", "property", "year", "amount", "date")
            self.preview_tree["columns"] = columns
            
            for col in columns:
                self.preview_tree.heading(col, text=col.capitalize())
            
            self.preview_tree.column("id", width=50, anchor=tk.CENTER)
            self.preview_tree.column("taxpayer", width=200)
            self.preview_tree.column("property", width=150)
            self.preview_tree.column("year", width=80, anchor=tk.CENTER)
            self.preview_tree.column("amount", width=100, anchor=tk.E)
            self.preview_tree.column("date", width=100, anchor=tk.CENTER)
            
            # Add sample data
            for i in range(1, 10):
                self.preview_tree.insert("", tk.END, values=(
                    i,
                    f"Contribuable {i}",
                    f"Terrain {i}",
                    "2023",
                    f"{i * 1000:.2f}",
                    "01/05/2023"
                ))
        
        elif report_type == "summary":
            columns = ("category", "count", "total_area", "total_tax", "paid", "unpaid")
            self.preview_tree["columns"] = columns
            
            self.preview_tree.heading("category", text="Catégorie")
            self.preview_tree.heading("count", text="Nombre")
            self.preview_tree.heading("total_area", text="Surface Totale")
            self.preview_tree.heading("total_tax", text="Taxe Totale")
            self.preview_tree.heading("paid", text="Payé")
            self.preview_tree.heading("unpaid", text="Non Payé")
            
            self.preview_tree.column("category", width=100)
            self.preview_tree.column("count", width=80, anchor=tk.CENTER)
            self.preview_tree.column("total_area", width=100, anchor=tk.E)
            self.preview_tree.column("total_tax", width=100, anchor=tk.E)
            self.preview_tree.column("paid", width=100, anchor=tk.E)
            self.preview_tree.column("unpaid", width=100, anchor=tk.E)
            
            # Add sample data
            for i in range(1, 4):
                self.preview_tree.insert("", tk.END, values=(
                    f"Catégorie {i}",
                    f"{i * 10}",
                    f"{i * 1000} m²",
                    f"{i * 50000:.2f} DH",
                    f"{i * 30000:.2f} DH",
                    f"{i * 20000:.2f} DH"
                ))
    
    def update_theme(self, theme):
        """Update the theme of all widgets."""
        self.theme = theme
        self.configure(bg=theme["bg"])
        
        # Update all child widgets (simplified for brevity)
        for widget in self.winfo_children():
            if isinstance(widget, tk.Frame):
                widget.configure(bg=theme["bg"])
    
    def browse_output_path(self):
        """Browse for output directory."""
        directory = filedialog.askdirectory(initialdir=self.path_entry.get())
        if directory:
            self.path_entry.delete(0, tk.END)
            self.path_entry.insert(0, directory)
    
    def generate_report(self):
        """Generate the selected report."""
        report_type = self.report_type_var.get()
        output_format = self.format_var.get()
        output_path = self.path_entry.get()
        
        # Validate output path
        if not os.path.exists(output_path):
            try:
                os.makedirs(output_path)
            except Exception as e:
                messagebox.showerror("Erreur", f"Impossible de créer le répertoire de sortie: {str(e)}")
                return
        
        # Generate report (this would be implemented to generate real reports)
        try:
            # Simulate report generation
            report_name = f"{report_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.{output_format}"
            report_path = os.path.join(output_path, report_name)
            
            # Show success message
            messagebox.showinfo("Succès", f"Rapport généré avec succès: {report_path}")
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de la génération du rapport: {str(e)}")
