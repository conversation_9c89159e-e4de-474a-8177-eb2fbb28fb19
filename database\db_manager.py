#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Database Manager for TNB Management System
"""

import sqlite3
import os
from datetime import datetime

class DatabaseManager:
    def __init__(self, db_path="tnb_management.db"):
        """Initialize the database manager with the specified database path."""
        self.db_path = db_path
        self.initialize_database()

    def get_connection(self):
        """Get a connection to the database."""
        return sqlite3.connect(self.db_path)

    def initialize_database(self):
        """Initialize the database with required tables if they don't exist."""
        conn = self.get_connection()
        cursor = conn.cursor()

        # Create Users table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password TEXT NOT NULL,
                full_name TEXT,
                role TEXT DEFAULT 'user',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # Create Taxpayers table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS taxpayers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                first_name TEXT NOT NULL,
                last_name TEXT NOT NULL,
                cin TEXT UNIQUE NOT NULL,
                address TEXT,
                fiscal_id TEXT UNIQUE,
                phone TEXT,
                email TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # Create Properties table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS properties (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                taxpayer_id INTEGER NOT NULL,
                reference TEXT UNIQUE NOT NULL,
                address TEXT NOT NULL,
                area REAL NOT NULL,
                zone TEXT NOT NULL,
                category TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (taxpayer_id) REFERENCES taxpayers (id) ON DELETE CASCADE
            )
        """)

        # Create Payments table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS payments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                taxpayer_id INTEGER NOT NULL,
                property_id INTEGER NOT NULL,
                year INTEGER NOT NULL,
                amount REAL NOT NULL,
                payment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                notes TEXT,
                receipt_number TEXT UNIQUE,
                created_by INTEGER,
                FOREIGN KEY (taxpayer_id) REFERENCES taxpayers (id) ON DELETE CASCADE,
                FOREIGN KEY (property_id) REFERENCES properties (id) ON DELETE CASCADE,
                FOREIGN KEY (created_by) REFERENCES users (id)
            )
        """)

        # Insert default admin user if not exists
        cursor.execute("""
            INSERT OR IGNORE INTO users (username, password, full_name, role)
            VALUES (?, ?, ?, ?)
        """, ("admin", "admin", "Administrator", "admin"))

        conn.commit()
        conn.close()

    # User management methods
    def authenticate_user(self, username, password):
        """Authenticate a user with the given username and password."""
        conn = self.get_connection()
        cursor = conn.cursor()

        cursor.execute("""
            SELECT id, username, full_name, role FROM users
            WHERE username = ? AND password = ?
        """, (username, password))

        user = cursor.fetchone()
        conn.close()

        if user:
            return {
                "id": user[0],
                "username": user[1],
                "full_name": user[2],
                "role": user[3]
            }
        return None

    # Taxpayer management methods
    def add_taxpayer(self, first_name, last_name, cin, address, fiscal_id=None, phone=None, email=None):
        """Add a new taxpayer to the database."""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute("""
                INSERT INTO taxpayers (first_name, last_name, cin, address, fiscal_id, phone, email)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (first_name, last_name, cin, address, fiscal_id, phone, email))

            taxpayer_id = cursor.lastrowid
            conn.commit()
            return taxpayer_id
        except sqlite3.IntegrityError as e:
            conn.rollback()
            if "UNIQUE constraint failed: taxpayers.cin" in str(e):
                raise ValueError("A taxpayer with this CIN already exists.")
            elif "UNIQUE constraint failed: taxpayers.fiscal_id" in str(e):
                raise ValueError("A taxpayer with this fiscal ID already exists.")
            else:
                raise e
        finally:
            conn.close()

    def get_taxpayers(self, search_term=None):
        """Get all taxpayers or search for taxpayers matching the search term."""
        conn = self.get_connection()
        cursor = conn.cursor()

        if search_term:
            search_pattern = f"%{search_term}%"
            cursor.execute("""
                SELECT id, first_name, last_name, cin, address, fiscal_id, phone, email
                FROM taxpayers
                WHERE first_name LIKE ? OR last_name LIKE ? OR cin LIKE ? OR fiscal_id LIKE ?
                ORDER BY last_name, first_name
            """, (search_pattern, search_pattern, search_pattern, search_pattern))
        else:
            cursor.execute("""
                SELECT id, first_name, last_name, cin, address, fiscal_id, phone, email
                FROM taxpayers
                ORDER BY last_name, first_name
            """)

        taxpayers = []
        for row in cursor.fetchall():
            taxpayers.append({
                "id": row[0],
                "first_name": row[1],
                "last_name": row[2],
                "cin": row[3],
                "address": row[4],
                "fiscal_id": row[5],
                "phone": row[6],
                "email": row[7]
            })

        conn.close()
        return taxpayers

    def get_taxpayer_by_id(self, taxpayer_id):
        """Get a taxpayer by ID."""
        conn = self.get_connection()
        cursor = conn.cursor()

        cursor.execute("""
            SELECT id, first_name, last_name, cin, address, fiscal_id, phone, email
            FROM taxpayers
            WHERE id = ?
        """, (taxpayer_id,))

        row = cursor.fetchone()
        conn.close()

        if row:
            return {
                "id": row[0],
                "first_name": row[1],
                "last_name": row[2],
                "cin": row[3],
                "address": row[4],
                "fiscal_id": row[5],
                "phone": row[6],
                "email": row[7]
            }
        return None

    def update_taxpayer(self, taxpayer_id, first_name, last_name, cin, address, fiscal_id=None, phone=None, email=None):
        """Update an existing taxpayer."""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute("""
                UPDATE taxpayers
                SET first_name = ?, last_name = ?, cin = ?, address = ?,
                    fiscal_id = ?, phone = ?, email = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            """, (first_name, last_name, cin, address, fiscal_id, phone, email, taxpayer_id))

            conn.commit()
            return cursor.rowcount > 0
        except sqlite3.IntegrityError as e:
            conn.rollback()
            if "UNIQUE constraint failed: taxpayers.cin" in str(e):
                raise ValueError("A taxpayer with this CIN already exists.")
            elif "UNIQUE constraint failed: taxpayers.fiscal_id" in str(e):
                raise ValueError("A taxpayer with this fiscal ID already exists.")
            else:
                raise e
        finally:
            conn.close()

    def delete_taxpayer(self, taxpayer_id):
        """Delete a taxpayer by ID."""
        conn = self.get_connection()
        cursor = conn.cursor()

        cursor.execute("DELETE FROM taxpayers WHERE id = ?", (taxpayer_id,))
        deleted = cursor.rowcount > 0

        conn.commit()
        conn.close()

        return deleted

    # Property management methods
    def add_property(self, taxpayer_id, reference, address, area, zone, category):
        """Add a new property for a taxpayer."""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute("""
                INSERT INTO properties (taxpayer_id, reference, address, area, zone, category)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (taxpayer_id, reference, address, area, zone, category))

            property_id = cursor.lastrowid
            conn.commit()
            return property_id
        except sqlite3.IntegrityError as e:
            conn.rollback()
            if "UNIQUE constraint failed: properties.reference" in str(e):
                raise ValueError("A property with this reference already exists.")
            else:
                raise e
        finally:
            conn.close()

    def get_properties_by_taxpayer(self, taxpayer_id):
        """Get all properties for a specific taxpayer."""
        conn = self.get_connection()
        cursor = conn.cursor()

        cursor.execute("""
            SELECT id, reference, address, area, zone, category
            FROM properties
            WHERE taxpayer_id = ?
            ORDER BY reference
        """, (taxpayer_id,))

        properties = []
        for row in cursor.fetchall():
            properties.append({
                "id": row[0],
                "reference": row[1],
                "address": row[2],
                "area": row[3],
                "zone": row[4],
                "category": row[5]
            })

        conn.close()
        return properties

    def update_property(self, property_id, reference, address, area, zone, category):
        """Update an existing property."""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute("""
                UPDATE properties
                SET reference = ?, address = ?, area = ?, zone = ?, category = ?
                WHERE id = ?
            """, (reference, address, area, zone, category, property_id))

            conn.commit()
            return cursor.rowcount > 0
        except sqlite3.IntegrityError as e:
            conn.rollback()
            if "UNIQUE constraint failed: properties.reference" in str(e):
                raise ValueError("A property with this reference already exists.")
            else:
                raise e
        finally:
            conn.close()

    def delete_property(self, property_id):
        """Delete a property by ID."""
        conn = self.get_connection()
        cursor = conn.cursor()

        cursor.execute("DELETE FROM properties WHERE id = ?", (property_id,))
        deleted = cursor.rowcount > 0

        conn.commit()
        conn.close()

        return deleted

    # Payment management methods
    def add_payment(self, taxpayer_id, property_id, year, amount, notes=None, created_by=None):
        """Add a new payment record."""
        conn = self.get_connection()
        cursor = conn.cursor()

        # Generate a unique receipt number
        now = datetime.now()
        receipt_number = f"TNB-{now.strftime('%Y%m%d%H%M%S')}-{taxpayer_id}-{property_id}"

        cursor.execute("""
            INSERT INTO payments (taxpayer_id, property_id, year, amount, notes, receipt_number, created_by)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, (taxpayer_id, property_id, year, amount, notes, receipt_number, created_by))

        payment_id = cursor.lastrowid
        conn.commit()
        conn.close()

        return payment_id, receipt_number

    def get_payments_by_taxpayer(self, taxpayer_id):
        """Get all payments for a specific taxpayer."""
        conn = self.get_connection()
        cursor = conn.cursor()

        cursor.execute("""
            SELECT p.id, p.property_id, pr.reference, p.year, p.amount, p.payment_date, p.notes, p.receipt_number
            FROM payments p
            JOIN properties pr ON p.property_id = pr.id
            WHERE p.taxpayer_id = ?
            ORDER BY p.payment_date DESC
        """, (taxpayer_id,))

        payments = []
        for row in cursor.fetchall():
            payments.append({
                "id": row[0],
                "property_id": row[1],
                "property_reference": row[2],
                "year": row[3],
                "amount": row[4],
                "payment_date": row[5],
                "notes": row[6],
                "receipt_number": row[7]
            })

        conn.close()
        return payments

    def get_payment_by_id(self, payment_id):
        """Get a payment by ID with detailed information for receipt generation."""
        conn = self.get_connection()
        cursor = conn.cursor()

        cursor.execute("""
            SELECT
                p.id, p.year, p.amount, p.payment_date, p.notes, p.receipt_number,
                t.id, t.first_name, t.last_name, t.cin, t.fiscal_id, t.address,
                pr.id, pr.reference, pr.address, pr.area, pr.zone, pr.category
            FROM payments p
            JOIN taxpayers t ON p.taxpayer_id = t.id
            JOIN properties pr ON p.property_id = pr.id
            WHERE p.id = ?
        """, (payment_id,))

        row = cursor.fetchone()
        conn.close()

        if row:
            return {
                "payment": {
                    "id": row[0],
                    "year": row[1],
                    "amount": row[2],
                    "payment_date": row[3],
                    "notes": row[4],
                    "receipt_number": row[5]
                },
                "taxpayer": {
                    "id": row[6],
                    "first_name": row[7],
                    "last_name": row[8],
                    "cin": row[9],
                    "fiscal_id": row[10],
                    "address": row[11]
                },
                "property": {
                    "id": row[12],
                    "reference": row[13],
                    "address": row[14],
                    "area": row[15],
                    "zone": row[16],
                    "category": row[17]
                }
            }
        return None
