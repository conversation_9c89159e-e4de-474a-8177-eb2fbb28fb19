#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test PDF Generator
"""

from utils.pdf_generator import TNBPDFGenerator

def test_pdf_generation():
    """Test PDF generation with sample data."""
    
    # Sample data matching the image
    data = {
        'agent': 'RAHMANI HICHAM',
        'cin': 'C163053',
        'rc': '',
        'if': '',
        'patente': '',
        'name': 'FATIMA KHLIFI TAGHZOUTI',
        'date_acquisition': '30-12-2022',
        'date_autorisation': '',
        'address': 'NR 60 RUE LAHSSEN LYOUSSI SEFROU',
        'lot': '',
        'titre_foncier': '472953',
        'superficie': '111.00',
        'zone': 'IMMEUBLE NON EQUIPE',
        'designation': 'NR 02 LOTISSEMENT MOULAY ABDERRAHMANE EL ARAFI HABBOUNA',
        'rubrique': '417.***********',
        'quittance': '',
        'date_vente': '',
        'date_declaration': '',
        'quittance_num': '3162 - 3162',
        'date_quittance': '15-05-2025',
        'date_traitement': '15-05-2025',
        'jours_retard': '0',
        'majoration_vente': '',
        'superficie_globale': ''
    }
    
    # Generate PDF
    pdf_generator = TNBPDFGenerator()
    pdf_path = pdf_generator.generate_payment_order_without_payment(data)
    
    print(f"✅ PDF généré avec succès: {pdf_path}")
    
    # Try to open the PDF
    import os
    import subprocess
    import platform
    
    if platform.system() == 'Windows':
        os.startfile(pdf_path)
    elif platform.system() == 'Darwin':  # macOS
        subprocess.call(['open', pdf_path])
    else:  # Linux
        subprocess.call(['xdg-open', pdf_path])

if __name__ == "__main__":
    test_pdf_generation()
