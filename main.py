#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
TNB Management System - Main Application

This application is designed to manage the "Taxe sur les Terrains Non Bâtis" (TNB)
in Morocco. It provides a user-friendly interface for managing taxpayers, properties,
and tax payments, as well as generating reports and receipts.
"""

import os
import sys
import tkinter as tk

from database import DatabaseManager
from pdf_generator import PDFGenerator
from gui import Application, LoginScreen

def main():
    """Main entry point for the application."""
    try:
        print("Starting application...")

        # Initialize database
        print("Initializing database...")
        db_manager = DatabaseManager()
        db_manager.initialize_database()

        # Initialize PDF generator
        print("Initializing PDF generator...")
        pdf_generator = PDFGenerator()

        # Create main application window
        print("Creating main window...")
        root = tk.Tk()
        root.title("TNB Management System")

        # Set window size and position
        window_width = 1200
        window_height = 800
        screen_width = root.winfo_screenwidth()
        screen_height = root.winfo_screenheight()
        center_x = int(screen_width/2 - window_width/2)
        center_y = int(screen_height/2 - window_height/2)
        root.geometry(f'{window_width}x{window_height}+{center_x}+{center_y}')

        # Create application
        print("Creating application...")
        app = Application(db_manager, pdf_generator)

        # Create and show login screen
        print("Creating login screen...")
        login_screen = LoginScreen(root, db_manager, lambda user: on_login_success(app, user))
        print("Login screen created, starting main loop...")

        # Start the main loop
        root.mainloop()
    except Exception as e:
        print(f"Error in main: {str(e)}")
        import traceback
        traceback.print_exc()

def on_login_success(app, user):
    """Handle successful login."""
    app.set_user(user)
    app.deiconify()  # Show the main application window

if __name__ == "__main__":
    main()
