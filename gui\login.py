#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Login Screen for TNB Management System
"""

import tkinter as tk
from tkinter import messagebox, ttk
from .styles import Styles

class LoginScreen(tk.Toplevel):
    def __init__(self, parent, db_manager, callback=None):
        """Initialize the login screen."""
        print("Initializing LoginScreen...")
        super().__init__(parent)
        self.parent = parent
        self.db_manager = db_manager
        self.callback = callback
        self.is_dark_mode = True
        self.theme = Styles.get_theme(self.is_dark_mode)
        print("LoginScreen theme initialized...")

        # Configure window
        print("Configuring login window...")
        self.title("Connexion - Système de Gestion TNB")
        self.geometry("400x350")
        self.resizable(False, False)
        self.protocol("WM_DELETE_WINDOW", self.on_close)
        self.configure(bg=self.theme["bg"])

        # Center window
        print("Centering login window...")
        self.center_window()

        # Create widgets
        print("Creating login widgets...")
        self.create_widgets()

        # Set focus to username entry
        self.username_entry.focus_set()

        # Make window modal
        print("Making login window modal...")
        self.transient(parent)
        self.grab_set()

        # Ensure window is visible and on top
        self.lift()
        self.attributes('-topmost', True)
        self.after_idle(self.attributes, '-topmost', False)

        # Force update to ensure window is shown
        self.update()
        print("Login window setup complete.")

    def center_window(self):
        """Center the window on the screen."""
        self.update_idletasks()
        width = self.winfo_width()
        height = self.winfo_height()
        x = (self.winfo_screenwidth() // 2) - (width // 2)
        y = (self.winfo_screenheight() // 2) - (height // 2)
        self.geometry(f"{width}x{height}+{x}+{y}")

    def create_widgets(self):
        """Create the login screen widgets."""
        # Main frame
        main_frame = tk.Frame(self, **Styles.get_frame_style(self.theme))
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # Title
        title_label = tk.Label(
            main_frame,
            text="Système de Gestion TNB",
            **Styles.get_label_style(self.theme, size="title", weight="bold")
        )
        title_label.pack(pady=(0, 20))

        # Subtitle
        subtitle_label = tk.Label(
            main_frame,
            text="Taxe sur les Terrains Non Bâtis",
            **Styles.get_label_style(self.theme, size="large")
        )
        subtitle_label.pack(pady=(0, 30))

        # Username
        username_frame = tk.Frame(main_frame, bg=self.theme["bg"])
        username_frame.pack(fill=tk.X, pady=5)

        username_label = tk.Label(
            username_frame,
            text="Nom d'utilisateur:",
            **Styles.get_label_style(self.theme)
        )
        username_label.pack(anchor=tk.W)

        self.username_entry = tk.Entry(
            username_frame,
            **Styles.get_entry_style(self.theme)
        )
        self.username_entry.pack(fill=tk.X, pady=5)

        # Password
        password_frame = tk.Frame(main_frame, bg=self.theme["bg"])
        password_frame.pack(fill=tk.X, pady=5)

        password_label = tk.Label(
            password_frame,
            text="Mot de passe:",
            **Styles.get_label_style(self.theme)
        )
        password_label.pack(anchor=tk.W)

        self.password_entry = tk.Entry(
            password_frame,
            show="•",
            **Styles.get_entry_style(self.theme)
        )
        self.password_entry.pack(fill=tk.X, pady=5)

        # Login button
        button_frame = tk.Frame(main_frame, bg=self.theme["bg"])
        button_frame.pack(fill=tk.X, pady=(20, 0))

        login_button = tk.Button(
            button_frame,
            text="Connexion",
            command=self.login,
            **Styles.get_button_style(self.theme)
        )
        login_button.pack(fill=tk.X)

        # Bind Enter key to login
        self.bind("<Return>", lambda event: self.login())

    def login(self):
        """Attempt to log in with the provided credentials."""
        username = self.username_entry.get().strip()
        password = self.password_entry.get().strip()

        if not username or not password:
            messagebox.showerror("Erreur", "Veuillez saisir un nom d'utilisateur et un mot de passe.")
            return

        user = self.db_manager.authenticate_user(username, password)

        if user:
            if self.callback:
                self.callback(user)
            self.destroy()
        else:
            messagebox.showerror("Erreur", "Nom d'utilisateur ou mot de passe incorrect.")
            self.password_entry.delete(0, tk.END)
            self.password_entry.focus_set()

    def on_close(self):
        """Handle window close event."""
        self.parent.destroy()  # Close the entire application
