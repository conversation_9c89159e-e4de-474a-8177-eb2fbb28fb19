#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Payments Frame for TNB Management System
"""

import tkinter as tk
from tkinter import ttk, messagebox
import os
from datetime import datetime
from .styles import Styles

class PaymentDialog(tk.Toplevel):
    def __init__(self, parent, app, callback=None):
        """Initialize the payment dialog."""
        super().__init__(parent)
        self.parent = parent
        self.app = app
        self.callback = callback
        self.theme = app.theme

        # Configure window
        self.title("Enregistrer un Paiement")
        self.geometry("500x550")
        self.resizable(False, False)
        self.configure(bg=self.theme["bg"])

        # Center window
        self.center_window()

        # Create widgets
        self.create_widgets()

        # Make window modal
        self.transient(parent)
        self.grab_set()

    def center_window(self):
        """Center the window on the screen."""
        self.update_idletasks()
        width = self.winfo_width()
        height = self.winfo_height()
        x = (self.winfo_screenwidth() // 2) - (width // 2)
        y = (self.winfo_screenheight() // 2) - (height // 2)
        self.geometry(f"{width}x{height}+{x}+{y}")

    def create_widgets(self):
        """Create the dialog widgets."""
        # Main frame
        main_frame = tk.Frame(self, **Styles.get_frame_style(self.theme))
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # Title
        title_label = tk.Label(
            main_frame,
            text="Enregistrer un Paiement",
            **Styles.get_label_style(self.theme, size="title", weight="bold")
        )
        title_label.pack(pady=(0, 20))

        # Form fields
        form_frame = tk.Frame(main_frame, bg=self.theme["bg"])
        form_frame.pack(fill=tk.BOTH, expand=True)

        # Taxpayer selection
        taxpayer_label = tk.Label(
            form_frame,
            text="Contribuable:",
            **Styles.get_label_style(self.theme)
        )
        taxpayer_label.grid(row=0, column=0, sticky=tk.W, pady=5)

        self.taxpayer_var = tk.StringVar()
        self.taxpayer_combo = ttk.Combobox(
            form_frame,
            textvariable=self.taxpayer_var,
            state="readonly"
        )
        self.taxpayer_combo.grid(row=0, column=1, sticky=tk.EW, pady=5, padx=5)
        self.taxpayer_combo.bind("<<ComboboxSelected>>", self.on_taxpayer_selected)

        # Property selection
        property_label = tk.Label(
            form_frame,
            text="Terrain:",
            **Styles.get_label_style(self.theme)
        )
        property_label.grid(row=1, column=0, sticky=tk.W, pady=5)

        self.property_var = tk.StringVar()
        self.property_combo = ttk.Combobox(
            form_frame,
            textvariable=self.property_var,
            state="readonly"
        )
        self.property_combo.grid(row=1, column=1, sticky=tk.EW, pady=5, padx=5)

        # Year
        year_label = tk.Label(
            form_frame,
            text="Année d'Imposition:",
            **Styles.get_label_style(self.theme)
        )
        year_label.grid(row=2, column=0, sticky=tk.W, pady=5)

        current_year = datetime.now().year
        years = [str(year) for year in range(current_year - 5, current_year + 2)]
        self.year_var = tk.StringVar(value=str(current_year))
        self.year_combo = ttk.Combobox(
            form_frame,
            textvariable=self.year_var,
            values=years,
            state="readonly"
        )
        self.year_combo.grid(row=2, column=1, sticky=tk.EW, pady=5, padx=5)

        # Amount
        amount_label = tk.Label(
            form_frame,
            text="Montant (DH):",
            **Styles.get_label_style(self.theme)
        )
        amount_label.grid(row=3, column=0, sticky=tk.W, pady=5)

        self.amount_entry = tk.Entry(
            form_frame,
            **Styles.get_entry_style(self.theme)
        )
        self.amount_entry.grid(row=3, column=1, sticky=tk.EW, pady=5, padx=5)

        # Notes
        notes_label = tk.Label(
            form_frame,
            text="Notes:",
            **Styles.get_label_style(self.theme)
        )
        notes_label.grid(row=4, column=0, sticky=tk.W, pady=5)

        self.notes_text = tk.Text(
            form_frame,
            height=5,
            width=30,
            **Styles.get_entry_style(self.theme)
        )
        self.notes_text.grid(row=4, column=1, sticky=tk.EW, pady=5, padx=5)

        # Configure grid
        form_frame.grid_columnconfigure(1, weight=1)

        # Buttons
        button_frame = tk.Frame(main_frame, bg=self.theme["bg"])
        button_frame.pack(fill=tk.X, pady=(20, 0))

        cancel_button = tk.Button(
            button_frame,
            text="Annuler",
            command=self.destroy,
            **Styles.get_button_style(self.theme, "danger")
        )
        cancel_button.pack(side=tk.LEFT, padx=5)

        save_button = tk.Button(
            button_frame,
            text="Enregistrer",
            command=self.save_payment,
            **Styles.get_button_style(self.theme, "success")
        )
        save_button.pack(side=tk.RIGHT, padx=5)

        # Load taxpayers
        self.load_taxpayers()

    def load_taxpayers(self):
        """Load taxpayers into the combobox."""
        taxpayers = self.app.db_manager.get_taxpayers()
        self.taxpayers = taxpayers

        taxpayer_options = []
        for taxpayer in taxpayers:
            taxpayer_options.append(f"{taxpayer['first_name']} {taxpayer['last_name']} ({taxpayer['cin']})")

        self.taxpayer_combo["values"] = taxpayer_options

    def on_taxpayer_selected(self, event):
        """Handle taxpayer selection event."""
        if not self.taxpayer_var.get():
            return

        # Get selected taxpayer
        selected_index = self.taxpayer_combo.current()
        taxpayer_id = self.taxpayers[selected_index]["id"]

        # Load properties for this taxpayer
        properties = self.app.db_manager.get_properties_by_taxpayer(taxpayer_id)
        self.properties = properties

        property_options = []
        for prop in properties:
            property_options.append(f"{prop['reference']} - {prop['address']}")

        self.property_combo["values"] = property_options

        if property_options:
            self.property_combo.current(0)

    def save_payment(self):
        """Save the payment data."""
        # Validate taxpayer selection
        if not self.taxpayer_var.get():
            messagebox.showerror("Erreur", "Veuillez sélectionner un contribuable.")
            return

        # Validate property selection
        if not self.property_var.get():
            messagebox.showerror("Erreur", "Veuillez sélectionner un terrain.")
            return

        # Get selected taxpayer
        selected_taxpayer_index = self.taxpayer_combo.current()
        taxpayer_id = self.taxpayers[selected_taxpayer_index]["id"]

        # Get selected property
        selected_property_index = self.property_combo.current()
        property_id = self.properties[selected_property_index]["id"]

        # Get year
        year = int(self.year_var.get())

        # Validate amount
        try:
            amount = float(self.amount_entry.get().strip().replace(',', '.'))
            if amount <= 0:
                raise ValueError("Le montant doit être supérieur à zéro.")
        except ValueError:
            messagebox.showerror("Erreur", "Veuillez saisir un montant valide.")
            return

        # Get notes
        notes = self.notes_text.get("1.0", tk.END).strip()

        # Get current user ID
        user_id = self.app.current_user["id"] if self.app.current_user else None

        try:
            # Save payment
            payment_id, receipt_number = self.app.db_manager.add_payment(
                taxpayer_id, property_id, year, amount, notes, user_id
            )

            # Show success message
            messagebox.showinfo("Succès", "Paiement enregistré avec succès.")

            # Ask if user wants to generate receipt
            if messagebox.askyesno("Reçu", "Voulez-vous générer un reçu de paiement?"):
                self.generate_receipt(payment_id)

            # Call callback if provided
            if self.callback:
                self.callback()

            # Close dialog
            self.destroy()
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de l'enregistrement du paiement: {str(e)}")

    def generate_receipt(self, payment_id):
        """Generate a receipt for the payment."""
        try:
            # Get payment data
            payment_data = self.app.db_manager.get_payment_by_id(payment_id)

            # Generate PDF
            pdf_path = self.app.pdf_generator.generate_receipt(payment_data)

            # Show success message
            messagebox.showinfo("Succès", f"Reçu généré avec succès: {pdf_path}")

            # Open PDF
            os.startfile(pdf_path)
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de la génération du reçu: {str(e)}")


class PaymentsFrame(tk.Frame):
    def __init__(self, parent, app):
        """Initialize the payments frame."""
        self.app = app
        self.theme = app.theme

        super().__init__(parent, bg=self.theme["bg"])

        # Create widgets
        self.create_widgets()

    def create_widgets(self):
        """Create the payments frame widgets."""
        # Main container with padding
        main_container = tk.Frame(self, bg=self.theme["bg"], padx=20, pady=20)
        main_container.pack(fill=tk.BOTH, expand=True)

        # Title and search bar
        top_frame = tk.Frame(main_container, bg=self.theme["bg"])
        top_frame.pack(fill=tk.X, pady=(0, 20))

        title_label = tk.Label(
            top_frame,
            text="Gestion des Paiements",
            **Styles.get_label_style(self.theme, size="title", weight="bold")
        )
        title_label.pack(side=tk.LEFT)

        # Search frame
        search_frame = tk.Frame(top_frame, bg=self.theme["bg"])
        search_frame.pack(side=tk.RIGHT)

        self.search_entry = tk.Entry(
            search_frame,
            width=30,
            **Styles.get_entry_style(self.theme)
        )
        self.search_entry.pack(side=tk.LEFT, padx=5)
        self.search_entry.insert(0, "Rechercher...")
        self.search_entry.bind("<FocusIn>", self.on_search_focus_in)
        self.search_entry.bind("<FocusOut>", self.on_search_focus_out)
        self.search_entry.bind("<Return>", lambda event: self.search_payments())

        search_button = tk.Button(
            search_frame,
            text="Rechercher",
            command=self.search_payments,
            **Styles.get_button_style(self.theme)
        )
        search_button.pack(side=tk.LEFT, padx=5)

        # Buttons frame
        buttons_frame = tk.Frame(main_container, bg=self.theme["bg"])
        buttons_frame.pack(fill=tk.X, pady=(0, 10))

        add_button = tk.Button(
            buttons_frame,
            text="Enregistrer un Paiement",
            command=self.show_add_dialog,
            **Styles.get_button_style(self.theme, "success")
        )
        add_button.pack(side=tk.LEFT, padx=5)

        generate_receipt_button = tk.Button(
            buttons_frame,
            text="Générer un Reçu",
            command=self.generate_receipt,
            **Styles.get_button_style(self.theme, "info")
        )
        generate_receipt_button.pack(side=tk.LEFT, padx=5)

        # Payments treeview
        tree_frame = tk.Frame(main_container, bg=self.theme["bg"])
        tree_frame.pack(fill=tk.BOTH, expand=True)

        columns = ("id", "taxpayer", "property", "year", "amount", "date", "receipt")
        self.tree = ttk.Treeview(
            tree_frame,
            columns=columns,
            show="headings",
            selectmode="browse"
        )

        # Define headings
        self.tree.heading("id", text="ID")
        self.tree.heading("taxpayer", text="Redevable")
        self.tree.heading("property", text="Terrain")
        self.tree.heading("year", text="Année")
        self.tree.heading("amount", text="Montant (DH)")
        self.tree.heading("date", text="Date de Paiement")
        self.tree.heading("receipt", text="N° de Reçu")

        # Define columns
        self.tree.column("id", width=50, anchor=tk.CENTER)
        self.tree.column("taxpayer", width=200)
        self.tree.column("property", width=200)
        self.tree.column("year", width=80, anchor=tk.CENTER)
        self.tree.column("amount", width=100, anchor=tk.E)
        self.tree.column("date", width=150, anchor=tk.CENTER)
        self.tree.column("receipt", width=200)

        # Add scrollbars
        y_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscroll=y_scrollbar.set)

        x_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.HORIZONTAL, command=self.tree.xview)
        self.tree.configure(xscroll=x_scrollbar.set)

        # Pack treeview and scrollbars
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        y_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        x_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)

        # Bind double-click event
        self.tree.bind("<Double-1>", lambda event: self.generate_receipt())

        # Load payments
        self.load_payments()

    def on_search_focus_in(self, event):
        """Handle search entry focus in event."""
        if self.search_entry.get() == "Rechercher...":
            self.search_entry.delete(0, tk.END)

    def on_search_focus_out(self, event):
        """Handle search entry focus out event."""
        if not self.search_entry.get():
            self.search_entry.insert(0, "Rechercher...")

    def update_theme(self, theme):
        """Update the theme of all widgets."""
        self.theme = theme
        self.configure(bg=theme["bg"])

        # Update all child widgets (simplified for brevity)
        for widget in self.winfo_children():
            if isinstance(widget, tk.Frame):
                widget.configure(bg=theme["bg"])

    def load_payments(self):
        """Load payments from the database."""
        # This would be implemented to load real data
        # For now, we'll just add sample data
        for i in range(1, 10):
            self.tree.insert("", tk.END, values=(
                i,
                f"Redevable {i}",
                f"Terrain {i}",
                "2023",
                f"{i * 1000:.2f}",
                "2023-05-01",
                f"TNB-2023050100{i}"
            ))

    def search_payments(self):
        """Search payments based on search entry."""
        # This would be implemented to search real data
        messagebox.showinfo("Recherche", "Fonctionnalité de recherche à implémenter.")

    def show_add_dialog(self):
        """Show dialog to add a new payment."""
        dialog = PaymentDialog(self, self.app, callback=self.load_payments)
        self.wait_window(dialog)

    def generate_receipt(self):
        """Generate a receipt for the selected payment."""
        selected_item = self.tree.selection()
        if not selected_item:
            messagebox.showwarning("Avertissement", "Veuillez sélectionner un paiement pour générer un reçu.")
            return

        # This would be implemented to generate a real receipt
        messagebox.showinfo("Reçu", "Fonctionnalité de génération de reçu à implémenter.")
