from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QPushButton, QTableWidget, QTableWidgetItem, QMessageBox)
import sqlite3

class TaxpayerManagement(QWidget):
    def __init__(self):
        super().__init__()

        self.setWindowTitle("Manage Taxpayers")
        self.setGeometry(100, 100, 800, 600)

        layout = QVBoxLayout()

        # Table to display taxpayers
        self.table = QTableWidget()
        self.table.setColumnCount(3)
        self.table.setHorizontalHeaderLabels(["Name", "National ID", "Address"])
        layout.addWidget(self.table)

        # Button to refresh taxpayers
        self.refresh_button = QPushButton("Refresh Taxpayers")
        self.refresh_button.clicked.connect(self.load_taxpayers)
        layout.addWidget(self.refresh_button)

        # Button to delete selected taxpayer
        self.delete_button = QPushButton("Delete Selected Taxpayer")
        self.delete_button.clicked.connect(self.delete_taxpayer)
        layout.addWidget(self.delete_button)

        self.setLayout(layout)
        self.load_taxpayers()

    def load_taxpayers(self):
        conn = sqlite3.connect("tnb_management.db")
        cursor = conn.cursor()

        cursor.execute("SELECT name, national_id, address FROM Taxpayers")
        taxpayers = cursor.fetchall()

        self.table.setRowCount(0)
        for row_number, row_data in enumerate(taxpayers):
            self.table.insertRow(row_number)
            for column_number, data in enumerate(row_data):
                self.table.setItem(row_number, column_number, QTableWidgetItem(str(data)))

        conn.close()

    def delete_taxpayer(self):
        selected_row = self.table.currentRow()
        if selected_row == -1:
            QMessageBox.warning(self, "Error", "No taxpayer selected.")
            return

        national_id = self.table.item(selected_row, 1).text()

        conn = sqlite3.connect("tnb_management.db")
        cursor = conn.cursor()

        cursor.execute("DELETE FROM Taxpayers WHERE national_id = ?", (national_id,))
        conn.commit()
        conn.close()

        QMessageBox.information(self, "Success", f"Taxpayer with National ID '{national_id}' deleted.")
        self.load_taxpayers()
