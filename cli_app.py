#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Command-line interface for the TNB Management System
"""

import os
import sys
from datetime import datetime
from database import DatabaseManager
from pdf_generator import PDFGenerator

class TNBCommandLineApp:
    def __init__(self):
        """Initialize the command-line application."""
        self.db_manager = DatabaseManager()
        self.pdf_generator = PDFGenerator()
        self.current_user = None
    
    def start(self):
        """Start the application."""
        print("\n===== TNB Management System =====")
        print("Welcome to the TNB Management System")
        
        # Login
        if not self.login():
            print("Login failed. Exiting.")
            return
        
        # Main menu
        self.show_main_menu()
    
    def login(self):
        """Handle user login."""
        print("\n----- Login -----")
        attempts = 3
        
        while attempts > 0:
            username = input("Username: ")
            password = input("Password: ")
            
            user = self.db_manager.authenticate_user(username, password)
            if user:
                self.current_user = user
                print(f"\nWelcome, {user['username']}!")
                return True
            
            attempts -= 1
            print(f"Invalid username or password. {attempts} attempts remaining.")
        
        return False
    
    def show_main_menu(self):
        """Show the main menu."""
        while True:
            print("\n----- Main Menu -----")
            print("1. Manage Taxpayers")
            print("2. Manage Properties")
            print("3. Manage Payments")
            print("4. Generate Reports")
            print("5. Exit")
            
            choice = input("\nEnter your choice (1-5): ")
            
            if choice == "1":
                self.manage_taxpayers()
            elif choice == "2":
                self.manage_properties()
            elif choice == "3":
                self.manage_payments()
            elif choice == "4":
                self.generate_reports()
            elif choice == "5":
                print("\nThank you for using TNB Management System. Goodbye!")
                break
            else:
                print("Invalid choice. Please try again.")
    
    def manage_taxpayers(self):
        """Manage taxpayers."""
        while True:
            print("\n----- Taxpayer Management -----")
            print("1. List All Taxpayers")
            print("2. Search Taxpayers")
            print("3. Add New Taxpayer")
            print("4. Edit Taxpayer")
            print("5. Delete Taxpayer")
            print("6. Back to Main Menu")
            
            choice = input("\nEnter your choice (1-6): ")
            
            if choice == "1":
                self.list_taxpayers()
            elif choice == "2":
                self.search_taxpayers()
            elif choice == "3":
                self.add_taxpayer()
            elif choice == "4":
                self.edit_taxpayer()
            elif choice == "5":
                self.delete_taxpayer()
            elif choice == "6":
                break
            else:
                print("Invalid choice. Please try again.")
    
    def list_taxpayers(self):
        """List all taxpayers."""
        print("\n----- All Taxpayers -----")
        taxpayers = self.db_manager.get_taxpayers()
        
        if not taxpayers:
            print("No taxpayers found.")
            return
        
        print(f"Found {len(taxpayers)} taxpayers:")
        for i, taxpayer in enumerate(taxpayers, 1):
            print(f"{i}. {taxpayer['first_name']} {taxpayer['last_name']} (CIN: {taxpayer['cin']}, ID: {taxpayer['id']})")
    
    def search_taxpayers(self):
        """Search for taxpayers."""
        print("\n----- Search Taxpayers -----")
        search_term = input("Enter search term (name, CIN, or fiscal ID): ")
        
        taxpayers = self.db_manager.get_taxpayers(search_term)
        
        if not taxpayers:
            print("No taxpayers found matching your search.")
            return
        
        print(f"Found {len(taxpayers)} taxpayers:")
        for i, taxpayer in enumerate(taxpayers, 1):
            print(f"{i}. {taxpayer['first_name']} {taxpayer['last_name']} (CIN: {taxpayer['cin']}, ID: {taxpayer['id']})")
    
    def add_taxpayer(self):
        """Add a new taxpayer."""
        print("\n----- Add New Taxpayer -----")
        
        first_name = input("First Name: ")
        last_name = input("Last Name: ")
        cin = input("CIN: ")
        address = input("Address: ")
        fiscal_id = input("Fiscal ID (optional): ") or None
        phone = input("Phone (optional): ") or None
        email = input("Email (optional): ") or None
        
        try:
            taxpayer_id = self.db_manager.add_taxpayer(
                first_name, last_name, cin, address, fiscal_id, phone, email
            )
            print(f"Taxpayer added successfully with ID: {taxpayer_id}")
        except ValueError as e:
            print(f"Error: {str(e)}")
    
    def edit_taxpayer(self):
        """Edit an existing taxpayer."""
        print("\n----- Edit Taxpayer -----")
        
        # First list taxpayers
        self.list_taxpayers()
        
        taxpayer_id = input("\nEnter the ID of the taxpayer to edit: ")
        if not taxpayer_id.isdigit():
            print("Invalid ID. Please enter a number.")
            return
        
        taxpayer = self.db_manager.get_taxpayer_by_id(int(taxpayer_id))
        if not taxpayer:
            print(f"No taxpayer found with ID: {taxpayer_id}")
            return
        
        print(f"\nEditing taxpayer: {taxpayer['first_name']} {taxpayer['last_name']}")
        
        first_name = input(f"First Name [{taxpayer['first_name']}]: ") or taxpayer['first_name']
        last_name = input(f"Last Name [{taxpayer['last_name']}]: ") or taxpayer['last_name']
        cin = input(f"CIN [{taxpayer['cin']}]: ") or taxpayer['cin']
        address = input(f"Address [{taxpayer['address']}]: ") or taxpayer['address']
        fiscal_id = input(f"Fiscal ID [{taxpayer['fiscal_id']}]: ") or taxpayer['fiscal_id']
        phone = input(f"Phone [{taxpayer['phone']}]: ") or taxpayer['phone']
        email = input(f"Email [{taxpayer['email']}]: ") or taxpayer['email']
        
        try:
            success = self.db_manager.update_taxpayer(
                taxpayer['id'], first_name, last_name, cin, address, fiscal_id, phone, email
            )
            if success:
                print("Taxpayer updated successfully.")
            else:
                print("Failed to update taxpayer.")
        except ValueError as e:
            print(f"Error: {str(e)}")
    
    def delete_taxpayer(self):
        """Delete a taxpayer."""
        print("\n----- Delete Taxpayer -----")
        
        # First list taxpayers
        self.list_taxpayers()
        
        taxpayer_id = input("\nEnter the ID of the taxpayer to delete: ")
        if not taxpayer_id.isdigit():
            print("Invalid ID. Please enter a number.")
            return
        
        taxpayer = self.db_manager.get_taxpayer_by_id(int(taxpayer_id))
        if not taxpayer:
            print(f"No taxpayer found with ID: {taxpayer_id}")
            return
        
        confirm = input(f"Are you sure you want to delete {taxpayer['first_name']} {taxpayer['last_name']}? (y/n): ")
        if confirm.lower() != 'y':
            print("Deletion cancelled.")
            return
        
        success = self.db_manager.delete_taxpayer(int(taxpayer_id))
        if success:
            print("Taxpayer deleted successfully.")
        else:
            print("Failed to delete taxpayer.")
    
    def manage_properties(self):
        """Manage properties."""
        # Similar implementation to manage_taxpayers
        print("\n----- Property Management -----")
        print("This feature is not fully implemented in the CLI version.")
    
    def manage_payments(self):
        """Manage payments."""
        # Similar implementation to manage_taxpayers
        print("\n----- Payment Management -----")
        print("This feature is not fully implemented in the CLI version.")
    
    def generate_reports(self):
        """Generate reports."""
        print("\n----- Report Generation -----")
        print("This feature is not fully implemented in the CLI version.")

def main():
    """Main entry point for the application."""
    app = TNBCommandLineApp()
    app.start()

if __name__ == "__main__":
    main()
