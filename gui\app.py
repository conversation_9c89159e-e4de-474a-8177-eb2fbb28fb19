#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Main Application Window for TNB Management System
"""

import tkinter as tk
from tkinter import ttk, messagebox
import os

from .styles import Styles
from .dashboard import DashboardFrame
from .taxpayers import TaxpayersFrame
from .payments import PaymentsFrame
from .reports import ReportsFrame
from .settings import SettingsFrame

class Application(tk.Tk):
    def __init__(self, db_manager, pdf_generator):
        """Initialize the main application window."""
        super().__init__()

        self.db_manager = db_manager
        self.pdf_generator = pdf_generator
        self.current_user = None
        self.is_dark_mode = True
        self.theme = Styles.get_theme(self.is_dark_mode)

        # Configure window
        self.title("Système de Gestion TNB")
        self.geometry("1200x800")
        self.minsize(800, 600)
        self.configure(bg=self.theme["bg"])

        # Create style for ttk widgets
        self.style = ttk.Style()
        self.configure_styles()

        # Create main container
        self.main_container = tk.Frame(self, bg=self.theme["bg"])
        self.main_container.pack(fill=tk.BOTH, expand=True)

        # Create header
        self.header_frame = tk.Frame(self.main_container, bg=self.theme["bg"], height=60)
        self.header_frame.pack(fill=tk.X, padx=10, pady=10)

        # App title
        self.title_label = tk.Label(
            self.header_frame,
            text="Système de Gestion TNB",
            **Styles.get_label_style(self.theme, size="header", weight="bold")
        )
        self.title_label.pack(side=tk.LEFT, padx=10)

        # User info
        self.user_frame = tk.Frame(self.header_frame, bg=self.theme["bg"])
        self.user_frame.pack(side=tk.RIGHT, padx=10)

        self.user_label = tk.Label(
            self.user_frame,
            text="",
            **Styles.get_label_style(self.theme)
        )
        self.user_label.pack(side=tk.LEFT, padx=5)

        self.logout_button = tk.Button(
            self.user_frame,
            text="Déconnexion",
            command=self.logout,
            **Styles.get_button_style(self.theme, "danger")
        )
        self.logout_button.pack(side=tk.LEFT, padx=5)

        # Create notebook for tabs
        self.notebook = ttk.Notebook(self.main_container)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Create frames for each tab
        self.dashboard_frame = DashboardFrame(self.notebook, self)
        self.taxpayers_frame = TaxpayersFrame(self.notebook, self)
        self.payments_frame = PaymentsFrame(self.notebook, self)
        self.reports_frame = ReportsFrame(self.notebook, self)
        self.settings_frame = SettingsFrame(self.notebook, self)

        # Add frames to notebook
        self.notebook.add(self.dashboard_frame, text="Tableau de Bord")
        self.notebook.add(self.taxpayers_frame, text="Redevables")
        self.notebook.add(self.payments_frame, text="Paiements")
        self.notebook.add(self.reports_frame, text="Rapports")
        self.notebook.add(self.settings_frame, text="Paramètres")

        # Create status bar
        self.status_bar = tk.Frame(self.main_container, bg=self.theme["bg"], height=25)
        self.status_bar.pack(fill=tk.X, side=tk.BOTTOM)

        self.status_label = tk.Label(
            self.status_bar,
            text="Prêt",
            anchor=tk.W,
            **Styles.get_label_style(self.theme, size="small")
        )
        self.status_label.pack(side=tk.LEFT, padx=10)

        # Bind tab change event
        self.notebook.bind("<<NotebookTabChanged>>", self.on_tab_changed)

        # Center window
        self.center_window()

    def center_window(self):
        """Center the window on the screen."""
        self.update_idletasks()
        width = self.winfo_width()
        height = self.winfo_height()
        x = (self.winfo_screenwidth() // 2) - (width // 2)
        y = (self.winfo_screenheight() // 2) - (height // 2)
        self.geometry(f"{width}x{height}+{x}+{y}")

    def configure_styles(self):
        """Configure ttk styles based on the current theme."""
        self.style.theme_use('clam')

        # Configure Notebook style
        self.style.configure(
            "TNotebook",
            background=self.theme["bg"],
            borderwidth=0
        )
        self.style.configure(
            "TNotebook.Tab",
            background=self.theme["bg"],
            foreground=self.theme["fg"],
            padding=[10, 5],
            font=("Arial", 10)
        )
        self.style.map(
            "TNotebook.Tab",
            background=[("selected", self.theme["highlight_bg"])],
            foreground=[("selected", self.theme["highlight_fg"])]
        )

        # Configure Treeview style
        self.style.configure(
            "Treeview",
            background=self.theme["entry_bg"],
            foreground=self.theme["entry_fg"],
            fieldbackground=self.theme["entry_bg"],
            font=("Arial", 10),
            rowheight=25,
            borderwidth=0
        )
        self.style.configure(
            "Treeview.Heading",
            background=self.theme["table_header_bg"],
            foreground=self.theme["table_header_fg"],
            font=("Arial", 10, "bold"),
            relief="flat"
        )
        self.style.map(
            "Treeview",
            background=[("selected", self.theme["highlight_bg"])],
            foreground=[("selected", self.theme["highlight_fg"])]
        )

    def set_user(self, user):
        """Set the current user and update the UI."""
        self.current_user = user
        self.user_label.config(text=f"Utilisateur: {user['username']}")

    def toggle_theme(self):
        """Toggle between light and dark mode."""
        self.is_dark_mode = not self.is_dark_mode
        self.theme = Styles.get_theme(self.is_dark_mode)

        # Update styles
        self.configure_styles()

        # Update main window
        self.configure(bg=self.theme["bg"])
        self.main_container.configure(bg=self.theme["bg"])
        self.header_frame.configure(bg=self.theme["bg"])
        self.title_label.configure(**Styles.get_label_style(self.theme, size="header", weight="bold"))
        self.user_frame.configure(bg=self.theme["bg"])
        self.user_label.configure(**Styles.get_label_style(self.theme))
        self.logout_button.configure(**Styles.get_button_style(self.theme, "danger"))
        self.status_bar.configure(bg=self.theme["bg"])
        self.status_label.configure(**Styles.get_label_style(self.theme, size="small"))

        # Update all frames
        self.dashboard_frame.update_theme(self.theme)
        self.taxpayers_frame.update_theme(self.theme)
        self.payments_frame.update_theme(self.theme)
        self.reports_frame.update_theme(self.theme)
        self.settings_frame.update_theme(self.theme)

    def set_status(self, message):
        """Set the status bar message."""
        self.status_label.config(text=message)

    def on_tab_changed(self, event):
        """Handle tab change event."""
        tab_id = self.notebook.select()
        tab_name = self.notebook.tab(tab_id, "text")
        self.set_status(f"Onglet actif: {tab_name}")

    def logout(self):
        """Log out the current user and show the login screen."""
        if messagebox.askyesno("Déconnexion", "Êtes-vous sûr de vouloir vous déconnecter?"):
            from .login import LoginScreen
            self.withdraw()
            login_screen = LoginScreen(self, self.db_manager, self.on_login_success)
            login_screen.wait_window()

    def on_login_success(self, user):
        """Handle successful login."""
        self.set_user(user)
        self.deiconify()
        self.notebook.select(0)  # Select dashboard tab
        self.dashboard_frame.refresh()
        self.set_status("Connexion réussie")
