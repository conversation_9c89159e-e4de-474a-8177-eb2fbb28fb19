#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Quittance Dialog for TNB Management System
"""

import tkinter as tk
from tkinter import messagebox
from datetime import datetime

class QuittanceDialog:
    def __init__(self, parent, payment_data=None):
        """Initialize the Quittance dialog."""
        self.parent = parent
        self.payment_data = payment_data or {}
        self.window = None
        self.create_window()

    def create_window(self):
        """Create the Quittance window."""
        self.window = tk.Toplevel(self.parent)
        self.window.title("QUITTANCE")
        self.window.geometry("900x700")
        self.window.configure(bg="#1a1a2e")
        self.window.transient(self.parent)
        self.window.grab_set()

        # Main container
        main_frame = tk.Frame(self.window, bg="#1a1a2e")
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Create header
        self.create_header(main_frame)

        # Create input section
        self.create_input_section(main_frame)

        # Create table section
        self.create_table_section(main_frame)

        # Create buttons section
        self.create_buttons_section(main_frame)

        # Load sample data
        self.load_sample_data()

    def create_header(self, parent):
        """Create the header with title and options."""
        header_frame = tk.Frame(parent, bg="#1a1a2e")
        header_frame.pack(fill=tk.X, pady=(0, 20))

        # Title with gradient-like effect
        title_frame = tk.Frame(header_frame, bg="#0f3460", relief=tk.RAISED, bd=3)
        title_frame.pack(fill=tk.X, pady=(0, 10))

        title_label = tk.Label(
            title_frame,
            text="QUITTANCE",
            bg="#0f3460",
            fg="#00d4ff",
            font=("Arial", 28, "bold"),
            pady=15
        )
        title_label.pack()

        # Options frame
        options_frame = tk.Frame(header_frame, bg="#1a1a2e")
        options_frame.pack(fill=tk.X)

        # Payment type options
        self.payment_type = tk.StringVar(value="Espèce")

        espece_radio = tk.Radiobutton(
            options_frame,
            text="Espèce",
            variable=self.payment_type,
            value="Espèce",
            bg="#1a1a2e",
            fg="white",
            font=("Arial", 12, "bold"),
            selectcolor="#0f3460",
            activebackground="#1a1a2e",
            activeforeground="white"
        )
        espece_radio.pack(side=tk.RIGHT, padx=20)

        cheque_radio = tk.Radiobutton(
            options_frame,
            text="Chèque",
            variable=self.payment_type,
            value="Chèque",
            bg="#1a1a2e",
            fg="#00d4ff",
            font=("Arial", 12, "bold"),
            selectcolor="#0f3460",
            activebackground="#1a1a2e",
            activeforeground="#00d4ff"
        )
        cheque_radio.pack(side=tk.RIGHT, padx=20)

    def create_input_section(self, parent):
        """Create the input section for receipt details."""
        input_frame = tk.Frame(parent, bg="#16213e", relief=tk.RAISED, bd=2)
        input_frame.pack(fill=tk.X, pady=(0, 20))

        # Input fields container with grid layout to align with table
        fields_frame = tk.Frame(input_frame, bg="#16213e")
        fields_frame.pack(fill=tk.X, padx=10, pady=15)

        # Configure grid columns to match table alignment
        fields_frame.grid_columnconfigure(0, weight=1)  # Majoration section
        fields_frame.grid_columnconfigure(1, weight=0)  # Spacer
        fields_frame.grid_columnconfigure(2, weight=0)  # Quittance section
        fields_frame.grid_columnconfigure(3, weight=0)  # Date section

        # Majoration info (left side)
        majoration_label = tk.Label(
            fields_frame,
            text="Majoration (F. déclaration de Vente ) :",
            bg="#16213e",
            fg="#00ff00",
            font=("Arial", 12, "bold")
        )
        majoration_label.grid(row=0, column=0, sticky="w", padx=(10, 0))

        # Right side container for Quittance and Date (aligned with table columns)
        right_container = tk.Frame(fields_frame, bg="#16213e")
        right_container.grid(row=0, column=2, columnspan=2, sticky="e", padx=(0, 20))

        # Quittance Number (positioned to align with "Numéro de la Quittance" column)
        quittance_container = tk.Frame(right_container, bg="#16213e")
        quittance_container.pack(side=tk.LEFT, padx=(0, 15))

        quittance_label = tk.Label(
            quittance_container,
            text="Quittance N°",
            bg="#16213e",
            fg="white",
            font=("Arial", 11, "bold")
        )
        quittance_label.pack()

        self.quittance_entry = tk.Entry(
            quittance_container,
            font=("Arial", 11),
            width=10,
            bg="white",
            fg="black",
            justify=tk.CENTER,
            relief=tk.RIDGE,
            bd=1
        )
        self.quittance_entry.pack(pady=(3, 0))

        # Date Quittance (positioned to align with "Date de la Quittance" column)
        date_container = tk.Frame(right_container, bg="#16213e")
        date_container.pack(side=tk.LEFT)

        date_label = tk.Label(
            date_container,
            text="Date Quittance",
            bg="#16213e",
            fg="white",
            font=("Arial", 11, "bold")
        )
        date_label.pack()

        self.date_entry = tk.Entry(
            date_container,
            font=("Arial", 11),
            width=12,
            bg="white",
            fg="black",
            justify=tk.CENTER,
            relief=tk.RIDGE,
            bd=1
        )
        self.date_entry.pack(pady=(3, 0))

    def create_table_section(self, parent):
        """Create the table section."""
        table_frame = tk.Frame(parent, bg="#1a1a2e")
        table_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))

        # Années title
        annees_label = tk.Label(
            table_frame,
            text="Années",
            bg="#1a1a2e",
            fg="#00ff00",
            font=("Arial", 24, "bold"),
            pady=10
        )
        annees_label.pack()

        # Create treeview for the table
        columns = [
            "Année", "Superficie\nm²", "Tarif\nDH / m²", "Montant\nPrincipal",
            "Majoration\n15 %", "Majoration\n10 %", "Majoration\n5 %",
            "Nbr\nde\nMois", "Majoration\n0.5 %", "Montant\nà payer",
            "Numéro\nde la\nQuittance", "Date\nde la\nQuittance"
        ]

        # Table container
        table_container = tk.Frame(table_frame, bg="#87ceeb")
        table_container.pack(fill=tk.BOTH, expand=True, padx=10)

        # Create table with grid
        self.create_data_table(table_container, columns)

    def create_data_table(self, parent, columns):
        """Create the data table with grid layout."""
        # Headers
        header_frame = tk.Frame(parent, bg="#4682b4")
        header_frame.pack(fill=tk.X)

        for i, col in enumerate(columns):
            header_label = tk.Label(
                header_frame,
                text=col,
                bg="#4682b4",
                fg="white",
                font=("Arial", 8, "bold"),
                relief=tk.RIDGE,
                bd=1,
                width=8,
                height=3
            )
            header_label.grid(row=0, column=i, sticky="ew", padx=1, pady=1)

        # Data rows
        data_frame = tk.Frame(parent, bg="#87ceeb")
        data_frame.pack(fill=tk.BOTH, expand=True)

        # Sample data row (2025)
        sample_data = ["2025", "111,00", "5,00", "555,00", "500,00", "55,50", "27,75", "2", "5,55", "1 143,80", "3162", "15-05-2025"]

        # Create checkbox for the row
        self.row_selected = tk.BooleanVar(value=True)
        checkbox = tk.Checkbutton(
            data_frame,
            variable=self.row_selected,
            bg="#87ceeb",
            activebackground="#87ceeb"
        )
        checkbox.grid(row=0, column=0, sticky="w", padx=2)

        # Data entries
        self.data_entries = []
        for i, value in enumerate(sample_data):
            entry = tk.Entry(
                data_frame,
                font=("Arial", 9),
                width=8,
                justify=tk.CENTER,
                relief=tk.RIDGE,
                bd=1
            )
            entry.grid(row=0, column=i+1, sticky="ew", padx=1, pady=1)
            entry.insert(0, value)
            self.data_entries.append(entry)

        # Empty rows for additional data
        for row in range(1, 8):
            for col in range(len(columns)):
                entry = tk.Entry(
                    data_frame,
                    font=("Arial", 9),
                    width=8,
                    justify=tk.CENTER,
                    relief=tk.RIDGE,
                    bd=1,
                    bg="#e6f3ff"
                )
                entry.grid(row=row, column=col, sticky="ew", padx=1, pady=1)

        # Total row
        total_frame = tk.Frame(parent, bg="#4682b4")
        total_frame.pack(fill=tk.X)

        tk.Label(total_frame, text="TOTAL", bg="#4682b4", fg="white", font=("Arial", 10, "bold"), width=8).grid(row=0, column=0, sticky="ew", padx=1, pady=1)

        total_values = ["", "", "555,00", "500,00", "55,50", "27,75", "", "5,55", "1 143,80", "", ""]
        for i, value in enumerate(total_values):
            total_entry = tk.Entry(
                total_frame,
                font=("Arial", 9, "bold"),
                width=8,
                justify=tk.CENTER,
                relief=tk.RIDGE,
                bd=1,
                bg="#4682b4",
                fg="white"
            )
            total_entry.grid(row=0, column=i+1, sticky="ew", padx=1, pady=1)
            if value:
                total_entry.insert(0, value)

    def create_buttons_section(self, parent):
        """Create the buttons section."""
        buttons_frame = tk.Frame(parent, bg="#1a1a2e")
        buttons_frame.pack(fill=tk.X)

        # Valider button
        valider_btn = tk.Button(
            buttons_frame,
            text="Valider",
            bg="#00ff00",
            fg="black",
            font=("Arial", 16, "bold"),
            command=self.validate_quittance,
            padx=30,
            pady=10,
            relief=tk.RAISED,
            bd=3
        )
        valider_btn.pack(side=tk.LEFT, padx=(50, 20))

        # Annuler button
        annuler_btn = tk.Button(
            buttons_frame,
            text="Annuler",
            bg="#ff4444",
            fg="white",
            font=("Arial", 16, "bold"),
            command=self.cancel_quittance,
            padx=30,
            pady=10,
            relief=tk.RAISED,
            bd=3
        )
        annuler_btn.pack(side=tk.RIGHT, padx=(20, 50))

    def load_sample_data(self):
        """Load sample data into the form."""
        # Set current date
        current_date = datetime.now().strftime("%d-%m-%Y")
        self.date_entry.insert(0, current_date)

        # Set sample quittance number
        self.quittance_entry.insert(0, "3162")

    def validate_quittance(self):
        """Validate and process the quittance."""
        quittance_num = self.quittance_entry.get()
        quittance_date = self.date_entry.get()
        payment_type = self.payment_type.get()

        if not quittance_num or not quittance_date:
            messagebox.showerror("Erreur", "Veuillez remplir tous les champs obligatoires!")
            return

        # Show confirmation
        result = messagebox.askyesno(
            "Confirmation",
            f"📋 Confirmer la quittance?\n\n"
            f"Numéro: {quittance_num}\n"
            f"Date: {quittance_date}\n"
            f"Type: {payment_type}\n\n"
            f"Voulez-vous valider cette quittance?"
        )

        if result:
            messagebox.showinfo(
                "Succès",
                f"✅ Quittance validée avec succès!\n\n"
                f"Numéro: {quittance_num}\n"
                f"Date: {quittance_date}"
            )
            self.window.destroy()

    def cancel_quittance(self):
        """Cancel the quittance dialog."""
        result = messagebox.askyesno(
            "Confirmation",
            "❌ Êtes-vous sûr de vouloir annuler?\n\n"
            "Toutes les données saisies seront perdues."
        )

        if result:
            self.window.destroy()
