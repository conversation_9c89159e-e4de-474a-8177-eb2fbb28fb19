#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
TNB Form Window for TNB Management System
"""

import tkinter as tk
from tkinter import ttk, messagebox
from .styles import Styles

class TNBFormWindow:
    def __init__(self, parent, db_manager, theme):
        """Initialize the TNB form window."""
        self.parent = parent
        self.db_manager = db_manager
        self.theme = theme
        self.window = None
        
    def show(self):
        """Show the TNB form window."""
        if self.window is not None:
            self.window.lift()
            return
            
        self.window = tk.Toplevel(self.parent)
        self.window.title("Fiche T.N.B - Taxe sur les Terrains Non Bâtis")
        self.window.geometry("800x600")
        self.window.configure(bg=self.theme["bg"])
        
        # Center the window
        self.window.transient(self.parent)
        self.window.grab_set()
        
        # Create the form
        self.create_form()
        
        # Handle window close
        self.window.protocol("WM_DELETE_WINDOW", self.on_close)
        
    def create_form(self):
        """Create the TNB form interface."""
        # Main container
        main_frame = tk.Frame(self.window, bg=self.theme["bg"], padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Header
        header_frame = tk.Frame(main_frame, bg=self.theme["bg"])
        header_frame.pack(fill=tk.X, pady=(0, 20))
        
        title_label = tk.Label(
            header_frame,
            text="📋 Fiche T.N.B",
            **Styles.get_label_style(self.theme, size="header", weight="bold")
        )
        title_label.pack()
        
        subtitle_label = tk.Label(
            header_frame,
            text="Taxe sur les Terrains Non Bâtis",
            **Styles.get_label_style(self.theme, size="large")
        )
        subtitle_label.pack()
        
        # Form sections
        self.create_taxpayer_section(main_frame)
        self.create_property_section(main_frame)
        self.create_calculation_section(main_frame)
        self.create_buttons_section(main_frame)
        
    def create_taxpayer_section(self, parent):
        """Create taxpayer information section."""
        section_frame = tk.LabelFrame(
            parent,
            text="👤 Informations du Redevable",
            bg=self.theme["entry_bg"],
            fg=self.theme["entry_fg"],
            font=("Arial", 12, "bold"),
            padx=15,
            pady=10
        )
        section_frame.pack(fill=tk.X, pady=10)
        
        # Taxpayer fields
        fields = [
            ("Nom complet:", "taxpayer_name"),
            ("CIN:", "taxpayer_cin"),
            ("Adresse:", "taxpayer_address"),
            ("Téléphone:", "taxpayer_phone")
        ]
        
        self.taxpayer_vars = {}
        for i, (label_text, var_name) in enumerate(fields):
            row = i // 2
            col = (i % 2) * 2
            
            label = tk.Label(
                section_frame,
                text=label_text,
                **Styles.get_label_style(self.theme)
            )
            label.grid(row=row, column=col, sticky=tk.W, padx=5, pady=5)
            
            var = tk.StringVar()
            entry = tk.Entry(
                section_frame,
                textvariable=var,
                **Styles.get_entry_style(self.theme)
            )
            entry.grid(row=row, column=col+1, sticky=tk.EW, padx=5, pady=5)
            
            self.taxpayer_vars[var_name] = var
            
        # Configure grid weights
        for i in range(4):
            section_frame.grid_columnconfigure(i, weight=1 if i % 2 == 1 else 0)
            
    def create_property_section(self, parent):
        """Create property information section."""
        section_frame = tk.LabelFrame(
            parent,
            text="🏞️ Informations du Terrain",
            bg=self.theme["entry_bg"],
            fg=self.theme["entry_fg"],
            font=("Arial", 12, "bold"),
            padx=15,
            pady=10
        )
        section_frame.pack(fill=tk.X, pady=10)
        
        # Property fields
        fields = [
            ("Titre foncier:", "property_title"),
            ("Superficie (m²):", "property_area"),
            ("Zone:", "property_zone"),
            ("Secteur:", "property_sector")
        ]
        
        self.property_vars = {}
        for i, (label_text, var_name) in enumerate(fields):
            row = i // 2
            col = (i % 2) * 2
            
            label = tk.Label(
                section_frame,
                text=label_text,
                **Styles.get_label_style(self.theme)
            )
            label.grid(row=row, column=col, sticky=tk.W, padx=5, pady=5)
            
            var = tk.StringVar()
            entry = tk.Entry(
                section_frame,
                textvariable=var,
                **Styles.get_entry_style(self.theme)
            )
            entry.grid(row=row, column=col+1, sticky=tk.EW, padx=5, pady=5)
            
            self.property_vars[var_name] = var
            
        # Configure grid weights
        for i in range(4):
            section_frame.grid_columnconfigure(i, weight=1 if i % 2 == 1 else 0)
            
    def create_calculation_section(self, parent):
        """Create tax calculation section."""
        section_frame = tk.LabelFrame(
            parent,
            text="💰 Calcul de la Taxe",
            bg=self.theme["entry_bg"],
            fg=self.theme["entry_fg"],
            font=("Arial", 12, "bold"),
            padx=15,
            pady=10
        )
        section_frame.pack(fill=tk.X, pady=10)
        
        # Calculation fields
        calc_frame = tk.Frame(section_frame, bg=self.theme["entry_bg"])
        calc_frame.pack(fill=tk.X)
        
        # Tax rate
        tk.Label(
            calc_frame,
            text="Taux de taxe (DH/m²):",
            **Styles.get_label_style(self.theme)
        ).grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        
        self.tax_rate_var = tk.StringVar(value="2.00")
        tk.Entry(
            calc_frame,
            textvariable=self.tax_rate_var,
            **Styles.get_entry_style(self.theme)
        ).grid(row=0, column=1, sticky=tk.EW, padx=5, pady=5)
        
        # Calculate button
        calc_btn = tk.Button(
            calc_frame,
            text="🧮 Calculer",
            command=self.calculate_tax,
            **Styles.get_button_style(self.theme, "info")
        )
        calc_btn.grid(row=0, column=2, padx=10, pady=5)
        
        # Results
        self.result_var = tk.StringVar(value="Montant total: 0.00 DH")
        result_label = tk.Label(
            calc_frame,
            textvariable=self.result_var,
            **Styles.get_label_style(self.theme, size="large", weight="bold")
        )
        result_label.grid(row=1, column=0, columnspan=3, pady=10)
        
        calc_frame.grid_columnconfigure(1, weight=1)
        
    def create_buttons_section(self, parent):
        """Create action buttons section."""
        buttons_frame = tk.Frame(parent, bg=self.theme["bg"])
        buttons_frame.pack(fill=tk.X, pady=20)
        
        # Action buttons
        buttons = [
            ("💾 Enregistrer", "success", self.save_form),
            ("🖨️ Imprimer", "info", self.print_form),
            ("📄 Générer PDF", "warning", self.generate_pdf),
            ("❌ Fermer", "danger", self.on_close)
        ]
        
        for text, style, command in buttons:
            btn = tk.Button(
                buttons_frame,
                text=text,
                command=command,
                **Styles.get_button_style(self.theme, style)
            )
            btn.pack(side=tk.LEFT, padx=5)
            
    def calculate_tax(self):
        """Calculate the tax amount."""
        try:
            area = float(self.property_vars["property_area"].get() or 0)
            rate = float(self.tax_rate_var.get() or 0)
            total = area * rate
            
            self.result_var.set(f"Montant total: {total:.2f} DH")
        except ValueError:
            messagebox.showerror("Erreur", "Veuillez entrer des valeurs numériques valides.")
            
    def save_form(self):
        """Save the TNB form."""
        messagebox.showinfo("Enregistrer", "Fonctionnalité d'enregistrement à implémenter.")
        
    def print_form(self):
        """Print the TNB form."""
        messagebox.showinfo("Imprimer", "Fonctionnalité d'impression à implémenter.")
        
    def generate_pdf(self):
        """Generate PDF of the TNB form."""
        messagebox.showinfo("PDF", "Génération PDF à implémenter.")
        
    def on_close(self):
        """Handle window close."""
        self.window.destroy()
        self.window = None
