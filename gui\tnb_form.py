#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
TNB Form Window for TNB Management System
"""

import tkinter as tk
from tkinter import ttk, messagebox
from .styles import Styles

class TNBFormWindow:
    def __init__(self, parent, db_manager, theme):
        """Initialize the TNB form window."""
        self.parent = parent
        self.db_manager = db_manager
        self.theme = theme
        self.window = None

    def show(self):
        """Show the TNB form window."""
        if self.window is not None:
            self.window.lift()
            return

        self.window = tk.Toplevel(self.parent)
        self.window.title("Fiche T.N.B - Taxe sur les Terrains Non Bâtis")
        self.window.geometry("1200x800")
        self.window.configure(bg="#2C3E50")  # Dark blue background like the image

        # Make window resizable
        self.window.resizable(True, True)

        # Center the window
        self.window.transient(self.parent)
        self.window.grab_set()

        # Create the form
        self.create_form()

        # Handle window close
        self.window.protocol("WM_DELETE_WINDOW", self.on_close)

    def create_form(self):
        """Create the TNB form interface."""
        # Main container
        main_frame = tk.Frame(self.window, bg="#2C3E50", padx=10, pady=10)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Top section with header and controls
        self.create_header_section(main_frame)

        # Middle section with main table
        self.create_main_table_section(main_frame)

        # Bottom section with payment details
        self.create_payment_section(main_frame)

        # Right sidebar
        self.create_sidebar(main_frame)

    def create_header_section(self, parent):
        """Create the header section with title and controls."""
        # Top toolbar with buttons
        toolbar_frame = tk.Frame(parent, bg="#34495E", relief=tk.RAISED, bd=1)
        toolbar_frame.pack(fill=tk.X, pady=(0, 5))

        # Toolbar buttons (like in the image)
        toolbar_buttons = [
            ("Quitter", self.on_close),
            ("Enregistrer", self.save_form),
            ("Nouvelle Fiche", self.new_form),
            ("Modifier", self.modify_form),
            ("Suppression", self.delete_form),
            ("O.V", self.show_order_form),
            ("Rôle Indiv", self.show_individual_role),
            ("MOTIFS", self.show_motifs),
            ("Transfert", self.transfer_form),
            ("Calculette", self.show_calculator),
            ("Scanner et Archivés", self.show_scanner)
        ]

        for text, command in toolbar_buttons:
            btn = tk.Button(
                toolbar_frame,
                text=text,
                command=command,
                bg="#5D6D7E",
                fg="white",
                font=("Arial", 9),
                relief=tk.RAISED,
                bd=1,
                padx=8,
                pady=2
            )
            btn.pack(side=tk.LEFT, padx=1, pady=2)

        # Main header frame
        header_frame = tk.Frame(parent, bg="#2C3E50")
        header_frame.pack(fill=tk.X, pady=(0, 10))

        # Left side - Secteur and Zone info
        left_frame = tk.Frame(header_frame, bg="#2C3E50")
        left_frame.pack(side=tk.LEFT, padx=5)

        # Container for secteur and zone
        secteur_container = tk.Frame(left_frame, bg="#2C3E50")
        secteur_container.pack()

        # Secteur section
        secteur_row = tk.Frame(secteur_container, bg="#2C3E50")
        secteur_row.pack(fill=tk.X, pady=2)

        secteur_label = tk.Label(
            secteur_row,
            text="Secteur",
            bg="#32CD32",  # Green color
            fg="white",
            font=("Arial", 10, "bold"),
            padx=8,
            pady=4,
            relief=tk.RAISED,
            bd=1
        )
        secteur_label.pack(side=tk.LEFT, padx=(0, 5))

        self.secteur_entry = tk.Entry(
            secteur_row,
            font=("Arial", 10),
            width=25,
            bg="white",
            fg="black",
            relief=tk.SUNKEN,
            bd=1
        )
        self.secteur_entry.pack(side=tk.LEFT)
        self.secteur_entry.insert(0, "SEFROU")  # Default value

        # Zone section
        zone_row = tk.Frame(secteur_container, bg="#2C3E50")
        zone_row.pack(fill=tk.X, pady=2)

        zone_label = tk.Label(
            zone_row,
            text="Zone",
            bg="#4169E1",  # Blue color
            fg="white",
            font=("Arial", 10, "bold"),
            padx=8,
            pady=4,
            relief=tk.RAISED,
            bd=1
        )
        zone_label.pack(side=tk.LEFT, padx=(0, 5))

        self.zone_entry = tk.Entry(
            zone_row,
            font=("Arial", 10),
            width=25,
            bg="white",
            fg="black",
            relief=tk.SUNKEN,
            bd=1
        )
        self.zone_entry.pack(side=tk.LEFT)

        # Center - Main title
        center_frame = tk.Frame(header_frame, bg="#2C3E50")
        center_frame.pack(side=tk.LEFT, expand=True, fill=tk.X, padx=20)

        # Fiche T.N.B title with gradient effect
        title_frame = tk.Frame(center_frame, bg="#1A252F", relief=tk.RAISED, bd=3)
        title_frame.pack(pady=10)

        tk.Label(
            title_frame,
            text="Fiche T.N.B",
            bg="#1A252F",
            fg="#FFD700",  # Gold color
            font=("Arial", 24, "bold"),
            padx=30,
            pady=10
        ).pack()



    def create_main_table_section(self, parent):
        """Create the main table section with taxpayer data."""
        # Container for the table
        table_container = tk.Frame(parent, bg="#2C3E50")
        table_container.pack(fill=tk.BOTH, expand=True, pady=10)

        # Left side - Controls (removed Nbre de Fiche)
        left_controls = tk.Frame(table_container, bg="#2C3E50")
        left_controls.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))

        # Redevables section
        redev_frame = tk.Frame(left_controls, bg="#FFD700", relief=tk.RAISED, bd=2)
        redev_frame.pack(pady=5)

        tk.Label(
            redev_frame,
            text="Redevables",
            bg="#FFD700",
            fg="black",
            font=("Arial", 12, "bold"),
            padx=10,
            pady=10
        ).pack()

        # Search box section (below Redevables)
        search_container = tk.Frame(left_controls, bg="#2C3E50")
        search_container.pack(pady=5, fill=tk.X)

        # Search text box (white background like in the image)
        self.search_var = tk.StringVar()
        self.search_entry = tk.Entry(
            search_container,
            textvariable=self.search_var,
            bg="white",
            fg="black",
            font=("Arial", 11),
            relief=tk.SUNKEN,
            bd=2,
            width=25
        )
        self.search_entry.pack(pady=2)
        self.search_entry.bind('<KeyRelease>', self.on_search_change)
        self.search_entry.bind('<Return>', self.perform_search)

        # Search buttons container
        search_buttons_frame = tk.Frame(search_container, bg="#2C3E50")
        search_buttons_frame.pack(pady=2)

        # "Nouveau" button (green like in the image) - linked to new_form
        nouveau_btn = tk.Button(
            search_buttons_frame,
            text="Nouveau",
            bg="#32CD32",
            fg="black",
            font=("Arial", 9, "bold"),
            relief=tk.RAISED,
            bd=2,
            padx=8,
            pady=2,
            command=self.new_form  # Same function as "Nouvelle Fiche" button
        )
        nouveau_btn.pack(side=tk.LEFT, padx=2)

        # "Date Quittance" button (blue like in the image)
        date_quit_btn = tk.Button(
            search_buttons_frame,
            text="Date Quittance",
            bg="#4169E1",
            fg="white",
            font=("Arial", 9, "bold"),
            relief=tk.RAISED,
            bd=2,
            padx=8,
            pady=2,
            command=self.show_date_quittance
        )
        date_quit_btn.pack(side=tk.LEFT, padx=2)

        # Date display (like in the image: 25-05-2025)
        date_display = tk.Label(
            search_container,
            text="25-05-2025",
            bg="#87CEEB",
            fg="black",
            font=("Arial", 10, "bold"),
            relief=tk.RAISED,
            bd=2,
            padx=5,
            pady=2
        )
        date_display.pack(pady=2)

        # Secteur section
        secteur_frame = tk.Frame(left_controls, bg="#32CD32", relief=tk.RAISED, bd=2)
        secteur_frame.pack(pady=5)

        tk.Label(
            secteur_frame,
            text="Secteur",
            bg="#32CD32",
            fg="black",
            font=("Arial", 12, "bold"),
            padx=15,
            pady=10
        ).pack()

        # Main table area
        table_frame = tk.Frame(table_container, bg="white", relief=tk.SUNKEN, bd=2)
        table_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # Create the table with scrollbars
        self.create_data_table(table_frame)

    def create_data_table(self, parent):
        """Create the main data table."""
        # Create Treeview for the table
        columns = (
            "N° Article", "N° RC", "N° CIN", "Nom", "Lot",
            "N° Titre Foncier", "Date en m²", "Date Acquisition",
            "Date", "Dat"
        )

        self.tree = ttk.Treeview(parent, columns=columns, show="headings", height=15)

        # Define column headings and widths
        column_widths = [80, 80, 100, 150, 80, 100, 100, 120, 100, 80]

        for i, col in enumerate(columns):
            self.tree.heading(col, text=col)
            self.tree.column(col, width=column_widths[i], minwidth=50)

        # Add sample data (similar to the image)
        sample_data = [
            ("000000", "C50105", "MOHAMED CHADMI", "IMMEUBLE I", "", "2633841", "96", "boudarkam", "26-08-2009", ""),
            ("000000", "UC7693", "BOUZIANI ABDELALI", "IMMEUBLE", "312", "2352741", "196", "errachad", "18-09-2008", "28-08-2009"),
            ("000000", "C10236", "BOUCHAHED ABDELLATIF", "IMMEUBLE", "104", "9724741", "113", "LOTISSEMENT LI", "31-12-2006", "14-11-2006"),
            ("000000", "CD6891", "KHADIJA AYAD", "IMMEUBLE I", "", "1867526", "104", "LOT A HAY ZAITO", "07-04-2015", ""),
            ("000000", "C131208", "EL HAZAZ HANAE", "IMMEUBLE", "719", "2759741", "126", "LOTISSEMENT ER", "31-12-2001", ""),
            ("000000", "A5H1589", "HICHAM BEN CHEROUN", "IMMEUBLE", "22", "2487441", "124", "LOTISSEMENT ER", "26-09-2021", ""),
        ]

        for i, data in enumerate(sample_data):
            # Alternate row colors
            tag = "evenrow" if i % 2 == 0 else "oddrow"
            self.tree.insert("", "end", values=data, tags=(tag,))

        # Configure row colors
        self.tree.tag_configure("evenrow", background="#E8F4FD")
        self.tree.tag_configure("oddrow", background="white")

        # Add scrollbars
        v_scrollbar = ttk.Scrollbar(parent, orient="vertical", command=self.tree.yview)
        h_scrollbar = ttk.Scrollbar(parent, orient="horizontal", command=self.tree.xview)

        self.tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # Pack everything
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)

    def create_payment_section(self, parent):
        """Create the payment section (Reste à payer)."""
        # Bottom section container
        bottom_container = tk.Frame(parent, bg="#2C3E50")
        bottom_container.pack(fill=tk.X, pady=10)

        # Left side - Request form
        left_frame = tk.Frame(bottom_container, bg="#E6E6FA", relief=tk.RAISED, bd=2)
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))

        tk.Label(
            left_frame,
            text="Veuillez demander la\nrégularisation\ndes rôles suivants",
            bg="#E6E6FA",
            fg="black",
            font=("Arial", 10),
            justify=tk.CENTER,
            padx=10,
            pady=10
        ).pack()

        # Simple table for years and amounts
        table_frame = tk.Frame(left_frame, bg="white", relief=tk.SUNKEN, bd=1)
        table_frame.pack(padx=5, pady=5)

        # Headers
        tk.Label(table_frame, text="Année", bg="lightblue", font=("Arial", 9, "bold"), width=8).grid(row=0, column=0, sticky="ew")
        tk.Label(table_frame, text="Montant", bg="lightblue", font=("Arial", 9, "bold"), width=10).grid(row=0, column=1, sticky="ew")

        # Sample rows
        for i in range(1, 6):
            tk.Entry(table_frame, width=8, font=("Arial", 9)).grid(row=i, column=0, padx=1, pady=1)
            tk.Entry(table_frame, width=10, font=("Arial", 9)).grid(row=i, column=1, padx=1, pady=1)

        # Main payment table
        payment_table_frame = tk.Frame(bottom_container, bg="white", relief=tk.SUNKEN, bd=2)
        payment_table_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # Payment table title
        title_frame = tk.Frame(payment_table_frame, bg="#4169E1")
        title_frame.pack(fill=tk.X)

        tk.Label(
            title_frame,
            text="Reste à payer",
            bg="#4169E1",
            fg="white",
            font=("Arial", 14, "bold"),
            pady=5
        ).pack()

        # Payment table headers
        headers = [
            "Année", "Superficie\nm²", "Tarif\nDH/m²", "Montant\nPrincipal",
            "Majoration\n15%", "Majoration\n10%", "Majoration\n5%",
            "Nbr\nde\nMois", "Majoration\n0.5%", "Montant\nà payer"
        ]

        header_frame = tk.Frame(payment_table_frame, bg="lightblue")
        header_frame.pack(fill=tk.X)

        for i, header in enumerate(headers):
            tk.Label(
                header_frame,
                text=header,
                bg="lightblue",
                fg="black",
                font=("Arial", 9, "bold"),
                relief=tk.RIDGE,
                bd=1,
                width=10,
                height=3
            ).grid(row=0, column=i, sticky="ew")

        # Payment table data area
        data_frame = tk.Frame(payment_table_frame, bg="white")
        data_frame.pack(fill=tk.BOTH, expand=True)

        # Add some sample rows
        for row in range(5):
            for col in range(len(headers)):
                entry = tk.Entry(
                    data_frame,
                    width=10,
                    font=("Arial", 9),
                    justify=tk.CENTER,
                    relief=tk.RIDGE,
                    bd=1
                )
                entry.grid(row=row, column=col, sticky="ew", padx=1, pady=1)

    def create_sidebar(self, parent):
        """Create the right sidebar with Liste Lots and Impression sections."""
        # Right sidebar
        sidebar = tk.Frame(parent, bg="#2C3E50")
        sidebar.pack(side=tk.RIGHT, fill=tk.Y, padx=(10, 0))

        # Liste Lots section (clickable)
        lots_frame = tk.Frame(sidebar, bg="#FFD700", relief=tk.RAISED, bd=2, cursor="hand2")
        lots_frame.pack(fill=tk.X, pady=5)
        lots_frame.bind("<Button-1>", lambda e: self.show_lots_list())

        lots_label = tk.Label(
            lots_frame,
            text="Liste Lots",
            bg="#FFD700",
            fg="black",
            font=("Arial", 12, "bold"),
            padx=15,
            pady=10,
            cursor="hand2"
        )
        lots_label.pack()
        lots_label.bind("<Button-1>", lambda e: self.show_lots_list())

        # Impression sections (clickable)
        impression_frame1 = tk.Frame(sidebar, bg="#8B4513", relief=tk.RAISED, bd=2, cursor="hand2")
        impression_frame1.pack(fill=tk.X, pady=2)
        impression_frame1.bind("<Button-1>", lambda e: self.print_with_payment())

        impression_label1 = tk.Label(
            impression_frame1,
            text="Impression\nAvec Règlement",
            bg="#8B4513",
            fg="white",
            font=("Arial", 10, "bold"),
            justify=tk.CENTER,
            padx=10,
            pady=10,
            cursor="hand2"
        )
        impression_label1.pack()
        impression_label1.bind("<Button-1>", lambda e: self.print_with_payment())

        impression_frame2 = tk.Frame(sidebar, bg="#32CD32", relief=tk.RAISED, bd=2, cursor="hand2")
        impression_frame2.pack(fill=tk.X, pady=2)
        impression_frame2.bind("<Button-1>", lambda e: self.print_without_payment())

        impression_label2 = tk.Label(
            impression_frame2,
            text="Impression\nSans Règlement",
            bg="#32CD32",
            fg="black",
            font=("Arial", 10, "bold"),
            justify=tk.CENTER,
            padx=10,
            pady=10,
            cursor="hand2"
        )
        impression_label2.pack()
        impression_label2.bind("<Button-1>", lambda e: self.print_without_payment())

    def on_close(self):
        """Handle window close."""
        self.window.destroy()
        self.window = None

    def save_form(self):
        """Save the current form data."""
        from tkinter import messagebox
        try:
            # Here you would implement actual saving logic
            messagebox.showinfo(
                "Enregistrer",
                "✅ Fiche T.N.B enregistrée avec succès!\n\n"
                "Les données ont été sauvegardées dans la base de données."
            )
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de l'enregistrement: {str(e)}")

    def new_form(self):
        """Create a new form / Add new redevable."""
        from tkinter import messagebox
        result = messagebox.askyesno(
            "Nouvelle Fiche / Nouveau Redevable",
            "➕ Voulez-vous créer une nouvelle fiche T.N.B?\n\n"
            "Cela permettra d'ajouter un nouveau redevable.\n"
            "Les données non sauvegardées seront perdues."
        )
        if result:
            # Clear all fields and reset form
            self.clear_form_fields()
            messagebox.showinfo(
                "Nouvelle Fiche",
                "📄 Nouvelle fiche créée!\n\n"
                "✅ Formulaire réinitialisé pour un nouveau redevable.\n"
                "Vous pouvez maintenant saisir les nouvelles données."
            )

    def clear_form_fields(self):
        """Clear all form fields for new entry."""
        try:
            # Clear search field
            if hasattr(self, 'search_var'):
                self.search_var.set("")

            # Clear secteur and zone fields
            if hasattr(self, 'secteur_entry'):
                self.secteur_entry.delete(0, tk.END)
                self.secteur_entry.insert(0, "SEFROU")  # Reset to default

            if hasattr(self, 'zone_entry'):
                self.zone_entry.delete(0, tk.END)

            # Clear table selection
            if hasattr(self, 'tree'):
                self.tree.selection_remove(self.tree.selection())

            # Reset to show all data
            self.show_all_data()

        except Exception as e:
            print(f"Error clearing form fields: {e}")

    def modify_form(self):
        """Modify the current form."""
        from tkinter import messagebox
        messagebox.showinfo(
            "Modifier",
            "✏️ Mode modification activé!\n\n"
            "Vous pouvez maintenant modifier les données de la fiche."
        )

    def delete_form(self):
        """Delete the current form."""
        from tkinter import messagebox
        result = messagebox.askyesnocancel(
            "Suppression",
            "⚠️ ATTENTION!\n\n"
            "Voulez-vous vraiment supprimer cette fiche T.N.B?\n"
            "Cette action est irréversible!"
        )
        if result:
            messagebox.showinfo("Suppression", "🗑️ Fiche supprimée avec succès!")

    def show_order_form(self):
        """Show order form (O.V)."""
        from tkinter import messagebox
        try:
            # Import and show payment order dialog
            from .payment_order import PaymentOrderDialog
            dialog = PaymentOrderDialog(self.window, self)
            self.window.wait_window(dialog)
        except ImportError:
            messagebox.showinfo(
                "Ordre de Versement",
                "📄 Ouverture de l'ordre de versement...\n\n"
                "Cette fonctionnalité permet de créer des ordres de paiement."
            )

    def show_individual_role(self):
        """Show individual role."""
        from tkinter import messagebox
        messagebox.showinfo(
            "Rôle Individuel",
            "👤 Affichage du rôle individuel\n\n"
            "Cette fonctionnalité affiche les détails du rôle pour un contribuable spécifique."
        )

    def show_motifs(self):
        """Show motifs dialog."""
        from tkinter import messagebox
        motifs = [
            "• Exonération totale",
            "• Exonération partielle",
            "• Changement de propriétaire",
            "• Modification de superficie",
            "• Erreur de calcul",
            "• Terrain vendu",
            "• Terrain construit"
        ]
        messagebox.showinfo(
            "MOTIFS",
            "📋 Motifs disponibles:\n\n" + "\n".join(motifs)
        )

    def transfer_form(self):
        """Transfer form to another sector."""
        from tkinter import messagebox
        result = messagebox.askyesno(
            "Transfert",
            "🔄 Transférer cette fiche vers un autre secteur?\n\n"
            "Sélectionnez le secteur de destination."
        )
        if result:
            messagebox.showinfo("Transfert", "✅ Fiche transférée avec succès!")

    def show_calculator(self):
        """Show tax calculator."""
        self.create_calculator_window()

    def show_scanner(self):
        """Show scanner and archives."""
        from tkinter import messagebox
        messagebox.showinfo(
            "Scanner et Archives",
            "📁 Gestion des documents numérisés\n\n"
            "• Scanner de nouveaux documents\n"
            "• Consulter les archives\n"
            "• Associer documents aux fiches"
        )

    def create_calculator_window(self):
        """Create a tax calculator window."""
        calc_window = tk.Toplevel(self.window)
        calc_window.title("🧮 Calculette T.N.B")
        calc_window.geometry("400x500")
        calc_window.configure(bg="#2C3E50")
        calc_window.transient(self.window)
        calc_window.grab_set()

        # Calculator header
        header = tk.Label(
            calc_window,
            text="🧮 Calculette T.N.B",
            bg="#2C3E50",
            fg="#FFD700",
            font=("Arial", 16, "bold"),
            pady=10
        )
        header.pack()

        # Calculator frame
        calc_frame = tk.Frame(calc_window, bg="white", relief=tk.RAISED, bd=2)
        calc_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # Input fields
        fields = [
            ("Superficie (m²):", "area"),
            ("Tarif (DH/m²):", "rate"),
            ("Année d'imposition:", "year"),
            ("Majoration 15% (mois):", "maj15"),
            ("Majoration 10% (mois):", "maj10"),
            ("Majoration 5% (mois):", "maj5"),
            ("Majoration 0.5% (mois):", "maj05")
        ]

        self.calc_vars = {}
        for i, (label_text, var_name) in enumerate(fields):
            tk.Label(
                calc_frame,
                text=label_text,
                bg="white",
                font=("Arial", 10),
                anchor="w"
            ).grid(row=i, column=0, sticky="w", padx=10, pady=5)

            var = tk.StringVar()
            entry = tk.Entry(
                calc_frame,
                textvariable=var,
                font=("Arial", 10),
                width=15
            )
            entry.grid(row=i, column=1, padx=10, pady=5)
            self.calc_vars[var_name] = var

        # Calculate button
        calc_btn = tk.Button(
            calc_frame,
            text="🧮 Calculer",
            command=self.calculate_tax,
            bg="#3498DB",
            fg="white",
            font=("Arial", 12, "bold"),
            pady=5
        )
        calc_btn.grid(row=len(fields), column=0, columnspan=2, pady=20)

        # Result display
        self.result_text = tk.Text(
            calc_frame,
            height=8,
            width=40,
            font=("Arial", 10),
            bg="#F8F9FA",
            relief=tk.SUNKEN,
            bd=2
        )
        self.result_text.grid(row=len(fields)+1, column=0, columnspan=2, padx=10, pady=10)

        # Close button
        close_btn = tk.Button(
            calc_frame,
            text="Fermer",
            command=calc_window.destroy,
            bg="#E74C3C",
            fg="white",
            font=("Arial", 10)
        )
        close_btn.grid(row=len(fields)+2, column=0, columnspan=2, pady=10)

    def calculate_tax(self):
        """Calculate tax with all majorations."""
        try:
            area = float(self.calc_vars["area"].get() or 0)
            rate = float(self.calc_vars["rate"].get() or 0)
            maj15_months = int(self.calc_vars["maj15"].get() or 0)
            maj10_months = int(self.calc_vars["maj10"].get() or 0)
            maj5_months = int(self.calc_vars["maj5"].get() or 0)
            maj05_months = int(self.calc_vars["maj05"].get() or 0)

            # Base calculation
            principal = area * rate

            # Majorations
            maj15 = principal * 0.15 * (maj15_months / 12)
            maj10 = principal * 0.10 * (maj10_months / 12)
            maj5 = principal * 0.05 * (maj5_months / 12)
            maj05 = principal * 0.005 * maj05_months

            total = principal + maj15 + maj10 + maj5 + maj05

            # Display results
            result = f"""
📊 RÉSULTATS DU CALCUL T.N.B
{'='*40}

🏞️  Superficie: {area:,.0f} m²
💰 Tarif: {rate:.2f} DH/m²
📅 Année: {self.calc_vars["year"].get()}

💵 MONTANT PRINCIPAL: {principal:,.2f} DH

📈 MAJORATIONS:
   • 15% ({maj15_months} mois): {maj15:,.2f} DH
   • 10% ({maj10_months} mois): {maj10:,.2f} DH
   • 5% ({maj5_months} mois): {maj5:,.2f} DH
   • 0.5% ({maj05_months} mois): {maj05:,.2f} DH

{'='*40}
💰 TOTAL À PAYER: {total:,.2f} DH
{'='*40}
            """

            self.result_text.delete(1.0, tk.END)
            self.result_text.insert(1.0, result)

        except ValueError:
            from tkinter import messagebox
            messagebox.showerror("Erreur", "Veuillez entrer des valeurs numériques valides.")

    def show_lots_list(self):
        """Show the lots list window."""
        lots_window = tk.Toplevel(self.window)
        lots_window.title("📋 Liste des Lots")
        lots_window.geometry("600x400")
        lots_window.configure(bg="#2C3E50")
        lots_window.transient(self.window)
        lots_window.grab_set()

        # Header
        header = tk.Label(
            lots_window,
            text="📋 Liste des Lots",
            bg="#2C3E50",
            fg="#FFD700",
            font=("Arial", 16, "bold"),
            pady=10
        )
        header.pack()

        # Lots table
        lots_frame = tk.Frame(lots_window, bg="white", relief=tk.RAISED, bd=2)
        lots_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # Create treeview for lots
        columns = ("Lot", "Zone", "Secteur", "Superficie", "Statut")
        lots_tree = ttk.Treeview(lots_frame, columns=columns, show="headings", height=12)

        for col in columns:
            lots_tree.heading(col, text=col)
            lots_tree.column(col, width=100)

        # Sample lots data
        lots_data = [
            ("LOT 001", "Zone A", "SETROU", "500 m²", "Occupé"),
            ("LOT 002", "Zone A", "SETROU", "750 m²", "Libre"),
            ("LOT 003", "Zone B", "SETROU", "300 m²", "Occupé"),
            ("LOT 004", "Zone B", "SETROU", "600 m²", "En cours"),
            ("LOT 005", "Zone C", "SETROU", "450 m²", "Libre"),
        ]

        for data in lots_data:
            lots_tree.insert("", "end", values=data)

        lots_tree.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Close button
        close_btn = tk.Button(
            lots_window,
            text="Fermer",
            command=lots_window.destroy,
            bg="#E74C3C",
            fg="white",
            font=("Arial", 10)
        )
        close_btn.pack(pady=10)

    def print_with_payment(self):
        """Print form with payment details."""
        from tkinter import messagebox
        result = messagebox.askyesno(
            "Impression avec Règlement",
            "🖨️ Imprimer la fiche avec les détails de règlement?\n\n"
            "Cette option inclura :\n"
            "• Les montants payés\n"
            "• L'historique des paiements\n"
            "• Le solde restant"
        )
        if result:
            messagebox.showinfo(
                "Impression",
                "✅ Impression lancée!\n\n"
                "📄 Fiche T.N.B avec règlement envoyée à l'imprimante."
            )

    def print_without_payment(self):
        """Print form without payment details."""
        from tkinter import messagebox
        result = messagebox.askyesno(
            "Impression sans Règlement",
            "🖨️ Imprimer la fiche sans les détails de règlement?\n\n"
            "Cette option inclura seulement :\n"
            "• Les informations du contribuable\n"
            "• Les détails du terrain\n"
            "• Le montant dû"
        )
        if result:
            messagebox.showinfo(
                "Impression",
                "✅ Impression lancée!\n\n"
                "📄 Fiche T.N.B sans règlement envoyée à l'imprimante."
            )

    def on_search_change(self, event):
        """Handle search text change (real-time search)."""
        search_text = self.search_var.get().lower()
        if len(search_text) >= 2:  # Start searching after 2 characters
            self.filter_table_data(search_text)

    def perform_search(self, event=None):
        """Perform search when Enter is pressed."""
        search_text = self.search_var.get().lower()
        if search_text:
            self.filter_table_data(search_text)
            from tkinter import messagebox
            messagebox.showinfo(
                "Recherche",
                f"🔍 Recherche effectuée pour: '{search_text}'\n\n"
                f"Résultats filtrés dans le tableau."
            )
        else:
            self.show_all_data()

    def filter_table_data(self, search_text):
        """Filter table data based on search text."""
        # Clear current data
        for item in self.tree.get_children():
            self.tree.delete(item)

        # Sample data (in real app, this would come from database)
        all_data = [
            ("000000", "C50105", "MOHAMED CHADMI", "IMMEUBLE I", "", "2633841", "96", "boudarkam", "26-08-2009", ""),
            ("000000", "UC7693", "BOUZIANI ABDELALI", "IMMEUBLE", "312", "2352741", "196", "errachad", "18-09-2008", "28-08-2009"),
            ("000000", "C10236", "BOUCHAHED ABDELLATIF", "IMMEUBLE", "104", "9724741", "113", "LOTISSEMENT LI", "31-12-2006", "14-11-2006"),
            ("000000", "CD6891", "KHADIJA AYAD", "IMMEUBLE I", "", "1867526", "104", "LOT A HAY ZAITO", "07-04-2015", ""),
            ("000000", "C131208", "EL HAZAZ HANAE", "IMMEUBLE", "719", "2759741", "126", "LOTISSEMENT ER", "31-12-2001", ""),
            ("000000", "A5H1589", "HICHAM BEN CHEROUN", "IMMEUBLE", "22", "2487441", "124", "LOTISSEMENT ER", "26-09-2021", ""),
        ]

        # Filter data based on search text
        filtered_data = []
        for row in all_data:
            # Search in name (index 2), RC (index 1), and other relevant fields
            if (search_text in row[2].lower() or  # Name
                search_text in row[1].lower() or  # RC
                search_text in row[4].lower() or  # Lot
                search_text in row[7].lower()):   # Location
                filtered_data.append(row)

        # Add filtered data to tree
        for i, data in enumerate(filtered_data):
            tag = "evenrow" if i % 2 == 0 else "oddrow"
            self.tree.insert("", "end", values=data, tags=(tag,))

    def show_all_data(self):
        """Show all data (clear filter)."""
        # Clear current data
        for item in self.tree.get_children():
            self.tree.delete(item)

        # Add all sample data
        sample_data = [
            ("000000", "C50105", "MOHAMED CHADMI", "IMMEUBLE I", "", "2633841", "96", "boudarkam", "26-08-2009", ""),
            ("000000", "UC7693", "BOUZIANI ABDELALI", "IMMEUBLE", "312", "2352741", "196", "errachad", "18-09-2008", "28-08-2009"),
            ("000000", "C10236", "BOUCHAHED ABDELLATIF", "IMMEUBLE", "104", "9724741", "113", "LOTISSEMENT LI", "31-12-2006", "14-11-2006"),
            ("000000", "CD6891", "KHADIJA AYAD", "IMMEUBLE I", "", "1867526", "104", "LOT A HAY ZAITO", "07-04-2015", ""),
            ("000000", "C131208", "EL HAZAZ HANAE", "IMMEUBLE", "719", "2759741", "126", "LOTISSEMENT ER", "31-12-2001", ""),
            ("000000", "A5H1589", "HICHAM BEN CHEROUN", "IMMEUBLE", "22", "2487441", "124", "LOTISSEMENT ER", "26-09-2021", ""),
        ]

        for i, data in enumerate(sample_data):
            tag = "evenrow" if i % 2 == 0 else "oddrow"
            self.tree.insert("", "end", values=data, tags=(tag,))

    def show_date_quittance(self):
        """Show date quittance dialog."""
        from tkinter import messagebox
        from datetime import datetime

        current_date = datetime.now().strftime("%d-%m-%Y")

        messagebox.showinfo(
            "Date Quittance",
            f"📅 Gestion des dates de quittance\n\n"
            f"Date actuelle: {current_date}\n"
            f"Date affichée: 25-05-2025\n\n"
            "Cette fonctionnalité permet de gérer les dates des quittances de paiement."
        )
