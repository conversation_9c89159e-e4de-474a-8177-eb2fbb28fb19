#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
PDF Generator for TNB Management System
"""

import os
from datetime import datetime
from reportlab.lib.pagesizes import A4
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont

# Register fonts for multilingual support
try:
    pdfmetrics.registerFont(TTFont('DejaVuSans', 'DejaVuSans.ttf'))
    pdfmetrics.registerFont(TTFont('DejaVuSans-Bold', 'DejaVuSans-Bold.ttf'))
except:
    print("Warning: DejaVu fonts not found. Using default fonts.")

class PDFGenerator:
    def __init__(self, output_dir="receipts"):
        """Initialize the PDF generator with the specified output directory."""
        self.output_dir = output_dir

        # Create output directory if it doesn't exist
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        # Define styles
        self.styles = getSampleStyleSheet()
        self.styles.add(ParagraphStyle(
            name='French',
            fontName='DejaVuSans',
            fontSize=10,
            leading=12
        ))
        self.styles.add(ParagraphStyle(
            name='FrenchBold',
            fontName='DejaVuSans-Bold',
            fontSize=10,
            leading=12
        ))
        self.styles.add(ParagraphStyle(
            name='TNBTitle',
            fontName='DejaVuSans-Bold',
            fontSize=16,
            leading=20,
            alignment=1  # Center alignment
        ))
        self.styles.add(ParagraphStyle(
            name='TNBSubtitle',
            fontName='DejaVuSans-Bold',
            fontSize=12,
            leading=14,
            alignment=1  # Center alignment
        ))

    def generate_receipt(self, payment_data):
        """Generate a payment receipt PDF."""
        # Extract data
        payment = payment_data["payment"]
        taxpayer = payment_data["taxpayer"]
        property_data = payment_data["property"]

        # Create filename
        filename = f"{self.output_dir}/receipt_{payment['receipt_number']}.pdf"

        # Create PDF document
        doc = SimpleDocTemplate(
            filename,
            pagesize=A4,
            rightMargin=72,
            leftMargin=72,
            topMargin=72,
            bottomMargin=72
        )

        # Build content
        content = []

        # Add title
        content.append(Paragraph("ROYAUME DU MAROC", self.styles["TNBTitle"]))
        content.append(Paragraph("MINISTÈRE DE L'INTÉRIEUR", self.styles["TNBSubtitle"]))
        content.append(Spacer(1, 20))
        content.append(Paragraph("REÇU DE PAIEMENT", self.styles["TNBTitle"]))
        content.append(Paragraph("TAXE SUR LES TERRAINS NON BÂTIS (TNB)", self.styles["TNBSubtitle"]))
        content.append(Spacer(1, 30))

        # Add receipt number and date
        receipt_date = datetime.strptime(payment["payment_date"], "%Y-%m-%d %H:%M:%S") if isinstance(payment["payment_date"], str) else payment["payment_date"]
        receipt_date_str = receipt_date.strftime("%d/%m/%Y")

        receipt_info = [
            ["N° de Reçu:", payment["receipt_number"]],
            ["Date de Paiement:", receipt_date_str],
            ["Année d'Imposition:", str(payment["year"])]
        ]

        receipt_table = Table(receipt_info, colWidths=[150, 300])
        receipt_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (0, -1), 'DejaVuSans-Bold'),
            ('FONTNAME', (1, 0), (1, -1), 'DejaVuSans'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
        ]))
        content.append(receipt_table)
        content.append(Spacer(1, 20))

        # Add taxpayer information
        content.append(Paragraph("INFORMATIONS DU CONTRIBUABLE", self.styles["TNBSubtitle"]))
        content.append(Spacer(1, 10))

        taxpayer_info = [
            ["Nom et Prénom:", f"{taxpayer['first_name']} {taxpayer['last_name']}"],
            ["CIN:", taxpayer["cin"]],
            ["Identifiant Fiscal:", taxpayer["fiscal_id"] or "Non spécifié"],
            ["Adresse:", taxpayer["address"] or "Non spécifiée"]
        ]

        taxpayer_table = Table(taxpayer_info, colWidths=[150, 300])
        taxpayer_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (0, -1), 'DejaVuSans-Bold'),
            ('FONTNAME', (1, 0), (1, -1), 'DejaVuSans'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
        ]))
        content.append(taxpayer_table)
        content.append(Spacer(1, 20))

        # Add property information
        content.append(Paragraph("INFORMATIONS DU TERRAIN", self.styles["TNBSubtitle"]))
        content.append(Spacer(1, 10))

        property_info = [
            ["Référence:", property_data["reference"]],
            ["Adresse:", property_data["address"]],
            ["Superficie:", f"{property_data['area']} m²"],
            ["Zone:", property_data["zone"]],
            ["Catégorie:", property_data["category"]]
        ]

        property_table = Table(property_info, colWidths=[150, 300])
        property_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (0, -1), 'DejaVuSans-Bold'),
            ('FONTNAME', (1, 0), (1, -1), 'DejaVuSans'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
        ]))
        content.append(property_table)
        content.append(Spacer(1, 20))

        # Add payment information
        content.append(Paragraph("DÉTAILS DU PAIEMENT", self.styles["TNBSubtitle"]))
        content.append(Spacer(1, 10))

        payment_info = [
            ["Montant Payé:", f"{payment['amount']:.2f} DH"],
            ["Notes:", payment["notes"] or "Aucune note"]
        ]

        payment_table = Table(payment_info, colWidths=[150, 300])
        payment_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (0, -1), 'DejaVuSans-Bold'),
            ('FONTNAME', (1, 0), (1, -1), 'DejaVuSans'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
        ]))
        content.append(payment_table)
        content.append(Spacer(1, 40))

        # Add signature area
        signature_info = [
            ["Signature du Contribuable:", "Signature et Cachet de l'Administration:"],
            ["", ""],
            ["", ""],
            ["", ""],
            ["_______________________", "_______________________"]
        ]

        signature_table = Table(signature_info, colWidths=[225, 225])
        signature_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (1, 0), 'DejaVuSans-Bold'),
            ('FONTNAME', (0, 1), (1, -1), 'DejaVuSans'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('ALIGN', (0, 0), (1, -1), 'CENTER'),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
        ]))
        content.append(signature_table)

        # Build PDF
        doc.build(content)

        return filename

    def generate_payment_order(self, order_data):
        """Generate a payment order PDF."""
        # Extract data
        taxpayer = order_data["taxpayer"]
        property_data = order_data["property"]
        payment = order_data["payment"]

        # Create filename
        order_number = f"OV-{datetime.now().strftime('%Y%m%d%H%M%S')}"
        filename = f"{self.output_dir}/ordre_{order_number}.pdf"

        # Create PDF document
        doc = SimpleDocTemplate(
            filename,
            pagesize=A4,
            rightMargin=72,
            leftMargin=72,
            topMargin=72,
            bottomMargin=72
        )

        # Build content
        content = []

        # Add title
        content.append(Paragraph("ROYAUME DU MAROC", self.styles["TNBTitle"]))
        content.append(Paragraph("MINISTÈRE DE L'INTÉRIEUR", self.styles["TNBSubtitle"]))
        content.append(Spacer(1, 20))
        content.append(Paragraph("ORDRE DE VERSEMENT", self.styles["TNBTitle"]))
        content.append(Paragraph("TAXE SUR LES TERRAINS NON BÂTIS (TNB)", self.styles["TNBSubtitle"]))
        content.append(Spacer(1, 30))

        # Add order number and date
        order_date = datetime.now()
        order_date_str = order_date.strftime("%d/%m/%Y")

        order_info = [
            ["N° d'Ordre:", order_number],
            ["Date d'Émission:", order_date_str],
            ["Année d'Imposition:", str(payment["year"])],
            ["Date d'Échéance:", payment["due_date"]]
        ]

        order_table = Table(order_info, colWidths=[150, 300])
        order_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (0, -1), 'DejaVuSans-Bold'),
            ('FONTNAME', (1, 0), (1, -1), 'DejaVuSans'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
        ]))
        content.append(order_table)
        content.append(Spacer(1, 20))

        # Add taxpayer information
        content.append(Paragraph("INFORMATIONS DU CONTRIBUABLE", self.styles["TNBSubtitle"]))
        content.append(Spacer(1, 10))

        taxpayer_info = [
            ["Nom et Prénom:", f"{taxpayer['first_name']} {taxpayer['last_name']}"],
            ["CIN:", taxpayer["cin"]],
            ["Identifiant Fiscal:", taxpayer["fiscal_id"] or "Non spécifié"],
            ["Adresse:", taxpayer["address"] or "Non spécifiée"]
        ]

        taxpayer_table = Table(taxpayer_info, colWidths=[150, 300])
        taxpayer_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (0, -1), 'DejaVuSans-Bold'),
            ('FONTNAME', (1, 0), (1, -1), 'DejaVuSans'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
        ]))
        content.append(taxpayer_table)
        content.append(Spacer(1, 20))

        # Add property information
        content.append(Paragraph("INFORMATIONS DU TERRAIN", self.styles["TNBSubtitle"]))
        content.append(Spacer(1, 10))

        property_info = [
            ["Référence:", property_data["reference"]],
            ["Adresse:", property_data["address"]],
            ["Superficie:", f"{property_data['area']} m²"],
            ["Zone:", property_data["zone"]],
            ["Catégorie:", property_data["category"]]
        ]

        property_table = Table(property_info, colWidths=[150, 300])
        property_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (0, -1), 'DejaVuSans-Bold'),
            ('FONTNAME', (1, 0), (1, -1), 'DejaVuSans'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
        ]))
        content.append(property_table)
        content.append(Spacer(1, 20))

        # Add payment information
        content.append(Paragraph("DÉTAILS DU PAIEMENT", self.styles["TNBSubtitle"]))
        content.append(Spacer(1, 10))

        payment_info = [
            ["Montant à Payer:", f"{payment['amount']:.2f} DH"],
            ["Notes:", payment["notes"] or "Aucune note"]
        ]

        payment_table = Table(payment_info, colWidths=[150, 300])
        payment_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (0, -1), 'DejaVuSans-Bold'),
            ('FONTNAME', (1, 0), (1, -1), 'DejaVuSans'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
        ]))
        content.append(payment_table)
        content.append(Spacer(1, 40))

        # Add notice
        notice_text = "Cet ordre de versement doit être présenté lors du paiement de la taxe. Le paiement doit être effectué avant la date d'échéance pour éviter des pénalités de retard."
        notice = Paragraph(notice_text, self.styles["French"])
        content.append(notice)
        content.append(Spacer(1, 20))

        # Add signature area
        signature_info = [
            ["Signature et Cachet de l'Administration:", ""],
            ["", ""],
            ["", ""],
            ["", ""],
            ["_______________________", ""]
        ]

        signature_table = Table(signature_info, colWidths=[300, 150])
        signature_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (0, 0), 'DejaVuSans-Bold'),
            ('FONTNAME', (0, 1), (0, -1), 'DejaVuSans'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('ALIGN', (0, 0), (0, -1), 'CENTER'),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
        ]))
        content.append(signature_table)

        # Build PDF
        doc.build(content)

        return filename

    def generate_property_report(self, report_data):
        """Generate a property report PDF with or without regulation."""
        # Extract data
        property_data = report_data["property"]
        taxpayer = report_data["taxpayer"]
        tax_history = report_data["tax_history"]
        remaining_payments = report_data["remaining_payments"]
        with_regulation = report_data["with_regulation"]

        # Create filename
        report_type = "avec_reglement" if with_regulation else "sans_reglement"
        filename = f"{self.output_dir}/fiche_tnb_{property_data['reference']}_{report_type}_{datetime.now().strftime('%Y%m%d%H%M%S')}.pdf"

        # Create PDF document
        doc = SimpleDocTemplate(
            filename,
            pagesize=A4,
            rightMargin=72,
            leftMargin=72,
            topMargin=72,
            bottomMargin=72
        )

        # Build content
        content = []

        # Add title
        content.append(Paragraph("ROYAUME DU MAROC", self.styles["TNBTitle"]))
        content.append(Paragraph("MINISTÈRE DE L'INTÉRIEUR", self.styles["TNBSubtitle"]))
        content.append(Spacer(1, 20))
        content.append(Paragraph("FICHE T.N.B", self.styles["TNBTitle"]))
        content.append(Paragraph("TAXE SUR LES TERRAINS NON BÂTIS", self.styles["TNBSubtitle"]))
        content.append(Spacer(1, 30))

        # Add property ID
        property_id_info = [
            ["N° de Fiche:", str(property_data["id"])],
            ["Référence:", property_data["reference"]],
            ["Date d'Édition:", datetime.now().strftime("%d/%m/%Y")]
        ]

        property_id_table = Table(property_id_info, colWidths=[150, 300])
        property_id_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (0, -1), 'DejaVuSans-Bold'),
            ('FONTNAME', (1, 0), (1, -1), 'DejaVuSans'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
        ]))
        content.append(property_id_table)
        content.append(Spacer(1, 20))

        # Add taxpayer information
        content.append(Paragraph("REDEVABLES", self.styles["TNBSubtitle"]))
        content.append(Spacer(1, 10))

        taxpayer_info = [
            ["Nom et Prénom:", f"{taxpayer['first_name']} {taxpayer['last_name']}"],
            ["CIN:", taxpayer["cin"]],
            ["Identifiant Fiscal:", taxpayer["fiscal_id"] or "Non spécifié"],
            ["Adresse:", taxpayer["address"] or "Non spécifiée"]
        ]

        taxpayer_table = Table(taxpayer_info, colWidths=[150, 300])
        taxpayer_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (0, -1), 'DejaVuSans-Bold'),
            ('FONTNAME', (1, 0), (1, -1), 'DejaVuSans'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
        ]))
        content.append(taxpayer_table)
        content.append(Spacer(1, 20))

        # Add property information
        content.append(Paragraph("DÉTAILS DU TERRAIN", self.styles["TNBSubtitle"]))
        content.append(Spacer(1, 10))

        property_details = [
            ["Secteur:", property_data.get("zone", "")],
            ["Zone:", property_data.get("category", "")],
            ["Superficie:", f"{property_data.get('area', 0)} m²"],
            ["Désignation:", property_data.get("address", "")],
            ["T.F:", property_data.get("reference", "")]
        ]

        property_details_table = Table(property_details, colWidths=[150, 300])
        property_details_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (0, -1), 'DejaVuSans-Bold'),
            ('FONTNAME', (1, 0), (1, -1), 'DejaVuSans'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
        ]))
        content.append(property_details_table)
        content.append(Spacer(1, 20))

        # Add tax history
        content.append(Paragraph("HISTORIQUE T.N.B", self.styles["TNBSubtitle"]))
        content.append(Spacer(1, 10))

        # Create tax history table headers
        history_headers = ["Année", "Article", "N° OV", "Date OV", "N° Quitt", "Date Quitt", "Montant", "Observations"]
        history_data = [history_headers]

        # Add tax history data
        total_paid = 0
        for payment in tax_history:
            # Format date
            payment_date = payment.get("payment_date", "")
            if payment_date and isinstance(payment_date, str):
                try:
                    date_obj = datetime.strptime(payment_date, "%Y-%m-%d %H:%M:%S")
                    payment_date = date_obj.strftime("%d-%m-%Y")
                except:
                    pass

            # Add row
            history_data.append([
                payment.get("year", ""),
                "",  # Article
                payment.get("receipt_number", ""),  # Using receipt number as OV number
                "",  # OV date
                payment.get("receipt_number", ""),
                payment_date,
                f"{payment.get('amount', 0):.2f}",
                payment.get("notes", "")
            ])

            # Add to total
            total_paid += float(payment.get("amount", 0))

        # Add total row
        history_data.append(["TOTAL", "", "", "", "", "", f"{total_paid:.2f}", ""])

        # Create table
        history_table = Table(history_data, colWidths=[50, 50, 60, 60, 60, 60, 70, 100])
        history_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, 0), 'DejaVuSans-Bold'),  # Header row
            ('FONTNAME', (0, -1), (-1, -1), 'DejaVuSans-Bold'),  # Total row
            ('FONTNAME', (0, 1), (-1, -2), 'DejaVuSans'),  # Data rows
            ('FONTSIZE', (0, 0), (-1, -1), 9),
            ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ]))
        content.append(history_table)
        content.append(Spacer(1, 20))