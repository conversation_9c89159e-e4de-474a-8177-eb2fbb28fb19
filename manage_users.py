from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QPushButton, QTableWidget, QTableWidgetItem, QMessageBox)
import sqlite3

class ManageUsers(QWidget):
    def __init__(self):
        super().__init__()

        self.setWindowTitle("Manage Users")
        self.setGeometry(100, 100, 600, 400)

        layout = QVBoxLayout()

        # Table to display users
        self.table = QTableWidget()
        self.table.setColumnCount(2)
        self.table.setHorizontalHeaderLabels(["Username", "Password"])
        layout.addWidget(self.table)

        # Button to refresh users
        self.refresh_button = QPushButton("Refresh Users")
        self.refresh_button.clicked.connect(self.load_users)
        layout.addWidget(self.refresh_button)

        # Button to delete selected user
        self.delete_button = QPushButton("Delete Selected User")
        self.delete_button.clicked.connect(self.delete_user)
        layout.addWidget(self.delete_button)

        self.setLayout(layout)
        self.load_users()

    def load_users(self):
        conn = sqlite3.connect("tnb_management.db")
        cursor = conn.cursor()

        cursor.execute("SELECT username, password FROM Users")
        users = cursor.fetchall()

        self.table.setRowCount(0)
        for row_number, row_data in enumerate(users):
            self.table.insertRow(row_number)
            for column_number, data in enumerate(row_data):
                self.table.setItem(row_number, column_number, QTableWidgetItem(str(data)))

        conn.close()

    def delete_user(self):
        selected_row = self.table.currentRow()
        if selected_row == -1:
            QMessageBox.warning(self, "Error", "No user selected.")
            return

        username = self.table.item(selected_row, 0).text()

        conn = sqlite3.connect("tnb_management.db")
        cursor = conn.cursor()

        cursor.execute("DELETE FROM Users WHERE username = ?", (username,))
        conn.commit()
        conn.close()

        QMessageBox.information(self, "Success", f"User '{username}' deleted.")
        self.load_users()
