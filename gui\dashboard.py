#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Dashboard Frame for TNB Management System
"""

import tkinter as tk
from tkinter import ttk
from datetime import datetime
import random
from .styles import Styles

class DashboardFrame(tk.Frame):
    def __init__(self, parent, app):
        """Initialize the dashboard frame."""
        self.app = app
        self.theme = app.theme

        super().__init__(parent, bg=self.theme["bg"])

        # Create widgets
        self.create_widgets()

    def create_widgets(self):
        """Create the dashboard widgets."""
        # Main container with padding
        main_container = tk.Frame(self, bg=self.theme["bg"], padx=20, pady=20)
        main_container.pack(fill=tk.BOTH, expand=True)

        # Header with title and navigation
        header_frame = tk.Frame(main_container, bg=self.theme["bg"])
        header_frame.pack(fill=tk.X, pady=(0, 20))

        # Title section
        title_frame = tk.Frame(header_frame, bg=self.theme["bg"])
        title_frame.pack(side=tk.LEFT)

        title_label = tk.Label(
            title_frame,
            text="TNB",
            **Styles.get_label_style(self.theme, size="header", weight="bold")
        )
        title_label.pack(side=tk.LEFT)

        subtitle_label = tk.Label(
            title_frame,
            text="Tableau de Bord",
            **Styles.get_label_style(self.theme, size="large")
        )
        subtitle_label.pack(side=tk.LEFT, padx=(10, 0))

        # Navigation section with quick action buttons
        nav_frame = tk.Frame(header_frame, bg=self.theme["bg"])
        nav_frame.pack(side=tk.RIGHT)

        # Quick action buttons
        quick_actions = [
            ("➕ Nouveau Redevable", "success", self.add_taxpayer),
            ("💰 Nouveau Paiement", "info", self.add_payment),
            ("📋 Fiche T.N.B", "primary", self.show_tnb_form),
            ("📄 O.V", "secondary", self.show_payment_order),
            ("📊 Rapports", "warning", self.generate_report),
            ("🔄 Actualiser", "default", self.refresh)
        ]

        for text, style, command in quick_actions:
            btn = tk.Button(
                nav_frame,
                text=text,
                command=command,
                **Styles.get_button_style(self.theme, style)
            )
            btn.pack(side=tk.LEFT, padx=5)

        # Top stats section - 4 cards in a row
        stats_frame = tk.Frame(main_container, bg=self.theme["bg"])
        stats_frame.pack(fill=tk.X, pady=10)
        stats_frame.grid_columnconfigure((0, 1, 2, 3), weight=1)

        # Create stat cards with icons and trend indicators
        self.create_stat_card(stats_frame, 0, 0, "Redevables", "0", "taxpayers", "👤", "+12% depuis le mois dernier")
        self.create_stat_card(stats_frame, 0, 1, "Terrains", "0", "properties", "🏞️", "+8% depuis le mois dernier")
        self.create_stat_card(stats_frame, 0, 2, "Paiements", "0", "payments", "💰", "+4% depuis le mois dernier")
        self.create_stat_card(stats_frame, 0, 3, "Montant Total", "0 DH", "amount", "💵", "+6% depuis le mois dernier")

        # Middle section with charts and tables
        middle_frame = tk.Frame(main_container, bg=self.theme["bg"])
        middle_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        middle_frame.grid_columnconfigure(0, weight=2)
        middle_frame.grid_columnconfigure(1, weight=1)
        middle_frame.grid_rowconfigure(0, weight=1)

        # Left chart - Monthly revenue
        revenue_frame = self.create_chart_frame(middle_frame, "📈 Revenus Mensuels", 0, 0)
        self.create_monthly_revenue_chart(revenue_frame)

        # Right chart - Traffic sources (pie chart)
        sources_frame = self.create_chart_frame(middle_frame, "🥧 Sources de Paiement", 0, 1)
        self.create_payment_sources_chart(sources_frame)

        # Bottom section - Recent activity
        activity_frame = self.create_chart_frame(main_container, "🕒 Activité Récente", has_filter=True)
        activity_frame.pack(fill=tk.BOTH, expand=True, pady=10)

        self.create_activity_table(activity_frame)

    def create_stat_card(self, parent, row, column, title, value, stat_type, icon="", trend=""):
        """Create a statistics card with icon and trend indicator."""
        card = tk.Frame(
            parent,
            bg=self.theme["entry_bg"],
            padx=15,
            pady=15,
            highlightbackground=self.theme["border"],
            highlightthickness=1,
            relief=tk.RAISED,
            borderwidth=0,
            cursor="hand2"  # Make it look clickable
        )
        card.grid(row=row, column=column, padx=10, pady=10, sticky=tk.NSEW)

        # Add hover effects
        def on_enter(event):
            card.configure(highlightbackground=self.theme["highlight_bg"], highlightthickness=2)

        def on_leave(event):
            card.configure(highlightbackground=self.theme["border"], highlightthickness=1)

        def on_click(event):
            # Navigate to relevant section based on stat type
            if stat_type == "taxpayers":
                self.app.notebook.select(1)  # Redevables tab
            elif stat_type == "payments":
                self.app.notebook.select(2)  # Paiements tab
            elif stat_type == "properties":
                self.app.notebook.select(1)  # Redevables tab (properties are managed there)
            elif stat_type == "amount":
                self.app.notebook.select(3)  # Reports tab

        card.bind("<Enter>", on_enter)
        card.bind("<Leave>", on_leave)
        card.bind("<Button-1>", on_click)

        # Title with icon
        title_frame = tk.Frame(card, bg=self.theme["entry_bg"])
        title_frame.pack(fill=tk.X, anchor=tk.W)

        if icon:
            icon_label = tk.Label(
                title_frame,
                text=icon,
                bg=self.theme["entry_bg"],
                fg=self.theme["entry_fg"],
                font=("Arial", 16)
            )
            icon_label.pack(side=tk.LEFT, padx=(0, 5))

        title_label = tk.Label(
            title_frame,
            text=title,
            bg=self.theme["entry_bg"],
            fg=self.theme["entry_fg"],
            font=("Arial", 12)
        )
        title_label.pack(side=tk.LEFT)

        # Value
        value_label = tk.Label(
            card,
            text=value,
            bg=self.theme["entry_bg"],
            fg=self.theme["highlight_bg"],
            font=("Arial", 24, "bold")
        )
        value_label.pack(anchor=tk.W, pady=10)

        # Trend indicator
        if trend:
            trend_label = tk.Label(
                card,
                text=trend,
                bg=self.theme["entry_bg"],
                fg="#28a745",  # Green color for positive trends
                font=("Arial", 10, "italic")
            )
            trend_label.pack(anchor=tk.W)

        # Store reference to value label for updates
        setattr(self, f"{stat_type}_value", value_label)

    def create_chart_frame(self, parent, title, row=None, column=None, has_filter=False):
        """Create a frame for charts or tables with title and optional filter."""
        frame = tk.Frame(
            parent,
            bg=self.theme["entry_bg"],
            padx=15,
            pady=15,
            highlightbackground=self.theme["border"],
            highlightthickness=1,
            relief=tk.RAISED,
            borderwidth=0
        )

        if row is not None and column is not None:
            frame.grid(row=row, column=column, padx=10, pady=10, sticky=tk.NSEW)

        # Header with title and optional filter
        header_frame = tk.Frame(frame, bg=self.theme["entry_bg"])
        header_frame.pack(fill=tk.X, anchor=tk.W, pady=(0, 10))

        title_label = tk.Label(
            header_frame,
            text=title,
            bg=self.theme["entry_bg"],
            fg=self.theme["entry_fg"],
            font=("Arial", 14, "bold")
        )
        title_label.pack(side=tk.LEFT)

        if has_filter:
            filter_button = tk.Button(
                header_frame,
                text="🔍 Filtre",
                **Styles.get_button_style(self.theme, "info")
            )
            filter_button.pack(side=tk.RIGHT)

        # Content frame
        content_frame = tk.Frame(frame, bg=self.theme["entry_bg"])
        content_frame.pack(fill=tk.BOTH, expand=True)

        return content_frame

    def create_monthly_revenue_chart(self, parent):
        """Create a simple monthly revenue chart using canvas."""
        # Create canvas for the chart
        canvas = tk.Canvas(
            parent,
            bg=self.theme["entry_bg"],
            highlightthickness=0,
            height=250
        )
        canvas.pack(fill=tk.BOTH, expand=True)

        # Draw chart background grid
        width = 700
        height = 200
        margin = 40

        # Draw axes
        canvas.create_line(margin, margin, margin, height+margin, fill=self.theme["border"])
        canvas.create_line(margin, height+margin, width+margin, height+margin, fill=self.theme["border"])

        # Draw grid lines
        for i in range(5):
            y = margin + i * height/4
            canvas.create_line(margin, y, width+margin, y, fill=self.theme["border"], dash=(2, 4))
            value_label = tk.Label(
                canvas,
                text=f"{(4-i)*5000} DH",
                bg=self.theme["entry_bg"],
                fg=self.theme["fg"],
                font=("Arial", 8)
            )
            value_label.place(x=margin-35, y=y-10)

        # Draw month labels
        months = ["Jan", "Fév", "Mar", "Avr", "Mai", "Jun", "Jul", "Aoû", "Sep", "Oct", "Nov", "Déc"]
        for i, month in enumerate(months):
            x = margin + (i+1) * width/12
            canvas.create_line(x, height+margin-5, x, height+margin+5, fill=self.theme["border"])
            month_label = tk.Label(
                canvas,
                text=month,
                bg=self.theme["entry_bg"],
                fg=self.theme["fg"],
                font=("Arial", 8)
            )
            month_label.place(x=x-10, y=height+margin+5)

        # Generate sample data for current and previous year
        current_year_data = [random.randint(5000, 20000) for _ in range(12)]
        prev_year_data = [random.randint(3000, 15000) for _ in range(12)]

        # Draw lines for both years
        self.draw_line_chart(canvas, current_year_data, margin, width, height, "#0078D7")  # Blue for current year
        self.draw_line_chart(canvas, prev_year_data, margin, width, height, "#9370DB")     # Purple for previous year

        # Add legend
        legend_frame = tk.Frame(parent, bg=self.theme["entry_bg"])
        legend_frame.pack(fill=tk.X, pady=10)

        # Current year legend
        current_year = datetime.now().year
        current_legend = tk.Frame(legend_frame, bg=self.theme["entry_bg"])
        current_legend.pack(side=tk.LEFT, padx=20)

        current_color = tk.Canvas(current_legend, width=10, height=10, bg="#0078D7", highlightthickness=0)
        current_color.pack(side=tk.LEFT, padx=5)

        current_label = tk.Label(
            current_legend,
            text=f"Revenus {current_year}",
            bg=self.theme["entry_bg"],
            fg=self.theme["fg"],
            font=("Arial", 10)
        )
        current_label.pack(side=tk.LEFT)

        # Previous year legend
        prev_legend = tk.Frame(legend_frame, bg=self.theme["entry_bg"])
        prev_legend.pack(side=tk.LEFT, padx=20)

        prev_color = tk.Canvas(prev_legend, width=10, height=10, bg="#9370DB", highlightthickness=0)
        prev_color.pack(side=tk.LEFT, padx=5)

        prev_label = tk.Label(
            prev_legend,
            text=f"Revenus {current_year-1}",
            bg=self.theme["entry_bg"],
            fg=self.theme["fg"],
            font=("Arial", 10)
        )
        prev_label.pack(side=tk.LEFT)

    def draw_line_chart(self, canvas, data, margin, width, height, color):
        """Draw a line chart on the canvas."""
        # Calculate points
        points = []
        max_value = 20000  # Maximum value for scaling

        for i, value in enumerate(data):
            x = margin + (i+1) * width/12
            y = margin + height - (value / max_value * height)
            points.append((x, y))

            # Draw point
            canvas.create_oval(x-4, y-4, x+4, y+4, fill=color, outline="")

        # Draw lines connecting points
        for i in range(len(points)-1):
            x1, y1 = points[i]
            x2, y2 = points[i+1]
            canvas.create_line(x1, y1, x2, y2, fill=color, width=2, smooth=True)

    def create_payment_sources_chart(self, parent):
        """Create a simple pie chart for payment sources."""
        # Create canvas for the chart
        canvas = tk.Canvas(
            parent,
            bg=self.theme["entry_bg"],
            highlightthickness=0,
            height=250,
            width=250
        )
        canvas.pack(fill=tk.BOTH, expand=True)

        # Define center and radius
        center_x = 125
        center_y = 125
        radius = 80

        # Sample data for payment sources
        sources = {
            "Espèces": 45,
            "Chèque": 25,
            "Virement": 20,
            "Carte": 10
        }

        # Define colors for each source
        colors = {
            "Espèces": "#4e73df",  # Blue
            "Chèque": "#1cc88a",   # Green
            "Virement": "#f6c23e", # Yellow
            "Carte": "#e74a3b"     # Red
        }

        # Draw pie chart
        start_angle = 0
        for source, percentage in sources.items():
            # Calculate angles
            angle = 360 * (percentage / 100)
            end_angle = start_angle + angle

            # Draw pie slice
            canvas.create_arc(
                center_x - radius, center_y - radius,
                center_x + radius, center_y + radius,
                start=start_angle, extent=angle,
                fill=colors[source], outline=""
            )

            # Update start angle for next slice
            start_angle = end_angle

        # Draw a white circle in the middle for donut effect
        inner_radius = 40
        canvas.create_oval(
            center_x - inner_radius, center_y - inner_radius,
            center_x + inner_radius, center_y + inner_radius,
            fill=self.theme["entry_bg"], outline=""
        )

        # Add legend
        legend_frame = tk.Frame(parent, bg=self.theme["entry_bg"])
        legend_frame.pack(fill=tk.X, pady=10)

        # Create legend items
        for i, (source, percentage) in enumerate(sources.items()):
            legend_item = tk.Frame(legend_frame, bg=self.theme["entry_bg"])
            legend_item.pack(anchor=tk.W, pady=2)

            color_box = tk.Canvas(legend_item, width=10, height=10, bg=colors[source], highlightthickness=0)
            color_box.pack(side=tk.LEFT, padx=5)

            source_label = tk.Label(
                legend_item,
                text=f"{source}: {percentage}%",
                bg=self.theme["entry_bg"],
                fg=self.theme["fg"],
                font=("Arial", 10)
            )
            source_label.pack(side=tk.LEFT)

    def create_activity_table(self, parent):
        """Create a table for recent activity."""
        # Create frame for the table
        table_frame = tk.Frame(parent, bg=self.theme["entry_bg"])
        table_frame.pack(fill=tk.BOTH, expand=True)

        # Create treeview for recent activity
        columns = ("user", "activity", "time", "status")
        self.activity_tree = ttk.Treeview(
            table_frame,
            columns=columns,
            show="headings",
            height=5
        )

        # Define headings
        self.activity_tree.heading("user", text="Utilisateur")
        self.activity_tree.heading("activity", text="Activité")
        self.activity_tree.heading("time", text="Temps")
        self.activity_tree.heading("status", text="Statut")

        # Define columns
        self.activity_tree.column("user", width=150)
        self.activity_tree.column("activity", width=300)
        self.activity_tree.column("time", width=150)
        self.activity_tree.column("status", width=100)

        # Add scrollbar
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.activity_tree.yview)
        self.activity_tree.configure(yscroll=scrollbar.set)

        # Pack treeview and scrollbar
        self.activity_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Sample data with status icons
        activities = [
            ("Mohammed Alami", "💰 Paiement effectué", "2 minutes", "✅ Complété"),
            ("Fatima Benani", "🔐 Connexion depuis un nouvel appareil", "15 minutes", "⏳ En attente"),
            ("Ahmed Tazi", "✏️ Mise à jour des informations", "1 heure", "✅ Complété"),
            ("Samira Idrissi", "❓ Demande de réinitialisation de mot de passe", "3 heures", "🔄 En cours"),
            ("Karim Berrada", "💰 Paiement effectué", "5 heures", "✅ Complété")
        ]

        # Insert sample data with tags
        for i, activity in enumerate(activities):
            item_id = self.activity_tree.insert("", tk.END, values=activity)

            # Apply alternating row colors
            if i % 2 == 0:
                self.activity_tree.item(item_id, tags=("evenrow",))
            else:
                self.activity_tree.item(item_id, tags=("oddrow",))

        # Configure tag colors for alternating rows
        self.activity_tree.tag_configure("evenrow", background=self.theme["entry_bg"])
        self.activity_tree.tag_configure("oddrow", background=self.theme["bg"])

        # Configure tag colors for status (keeping original for compatibility)
        self.activity_tree.tag_configure("completed", background="#28a745")
        self.activity_tree.tag_configure("pending", background="#ffc107")
        self.activity_tree.tag_configure("in_progress", background="#17a2b8")

    def update_theme(self, theme):
        """Update the theme of all widgets."""
        self.theme = theme
        self.configure(bg=theme["bg"])

        # Update all child widgets
        for widget in self.winfo_children():
            if isinstance(widget, tk.Frame):
                widget.configure(bg=theme["bg"])
                for child in widget.winfo_children():
                    if isinstance(child, tk.Label):
                        child.configure(bg=theme["bg"], fg=theme["fg"])
                    elif isinstance(child, tk.Frame):
                        if "entry_bg" in child.configure():
                            child.configure(bg=theme["entry_bg"], highlightbackground=theme["border"])
                            for grandchild in child.winfo_children():
                                if isinstance(grandchild, tk.Label):
                                    grandchild.configure(bg=theme["entry_bg"], fg=theme["entry_fg"])
                                    if "highlight_bg" in grandchild.configure():
                                        grandchild.configure(fg=theme["highlight_bg"])
                    elif isinstance(child, tk.Button):
                        button_type = "default"
                        child.configure(**Styles.get_button_style(theme, button_type))

    def refresh(self):
        """Refresh the dashboard data."""
        # This would be implemented to fetch real data from the database
        # For now, we'll just update with sample data
        try:
            # Get actual counts from database if possible
            taxpayers_count = len(self.app.db_manager.get_taxpayers()) if hasattr(self.app, "db_manager") else 42
            properties_count = 78  # This would be fetched from database
            payments_count = 156   # This would be fetched from database
            total_amount = 234567  # This would be fetched from database

            # Format the numbers
            if hasattr(self, "taxpayers_value"):
                self.taxpayers_value.config(text=f"{taxpayers_count:,}")

            if hasattr(self, "properties_value"):
                self.properties_value.config(text=f"{properties_count:,}")

            if hasattr(self, "payments_value"):
                self.payments_value.config(text=f"{payments_count:,}")

            if hasattr(self, "amount_value"):
                self.amount_value.config(text=f"{total_amount:,.2f} DH")

            # Update recent activity if possible
            if hasattr(self.app, "db_manager") and hasattr(self, "activity_tree"):
                # Clear existing items
                for item in self.activity_tree.get_children():
                    self.activity_tree.delete(item)

                # Get recent activities (this would be implemented in the database manager)
                # For now, we'll use sample data with icons
                activities = [
                    ("Mohammed Alami", "💰 Paiement effectué", "2 minutes", "✅ Complété"),
                    ("Fatima Benani", "🔐 Connexion depuis un nouvel appareil", "15 minutes", "⏳ En attente"),
                    ("Ahmed Tazi", "✏️ Mise à jour des informations", "1 heure", "✅ Complété"),
                    ("Samira Idrissi", "❓ Demande de réinitialisation de mot de passe", "3 heures", "🔄 En cours"),
                    ("Karim Berrada", "💰 Paiement effectué", "5 heures", "✅ Complété")
                ]

                # Insert activities with alternating row colors
                for i, activity in enumerate(activities):
                    item_id = self.activity_tree.insert("", tk.END, values=activity)

                    # Apply alternating row colors
                    if i % 2 == 0:
                        self.activity_tree.item(item_id, tags=("evenrow",))
                    else:
                        self.activity_tree.item(item_id, tags=("oddrow",))
        except Exception as e:
            print(f"Error refreshing dashboard: {str(e)}")

    def add_taxpayer(self):
        """Navigate to the taxpayers tab and trigger add taxpayer."""
        self.app.notebook.select(1)  # Select taxpayers tab
        self.app.taxpayers_frame.show_add_dialog()

    def add_payment(self):
        """Navigate to the payments tab and trigger add payment."""
        self.app.notebook.select(2)  # Select payments tab
        self.app.payments_frame.show_add_dialog()

    def generate_report(self):
        """Navigate to the reports tab."""
        self.app.notebook.select(3)  # Select reports tab

    def search_taxpayer(self):
        """Navigate to the taxpayers tab and focus on search."""
        self.app.notebook.select(1)  # Select taxpayers tab
        self.app.taxpayers_frame.focus_search()

    def show_payment_order(self):
        """Show the payment order dialog."""
        from .payment_order import PaymentOrderDialog
        dialog = PaymentOrderDialog(self, self.app)
        self.wait_window(dialog)

    def show_tnb_form(self):
        """Show the TNB form window."""
        try:
            # Import the TNB form module
            from .tnb_form import TNBFormWindow

            # Create and show the TNB form window
            tnb_window = TNBFormWindow(self.app.root, self.app.db_manager, self.theme)
            tnb_window.show()
        except ImportError:
            # If TNB form doesn't exist yet, show a message
            import tkinter.messagebox as messagebox
            messagebox.showinfo(
                "Fiche T.N.B",
                "La fenêtre Fiche T.N.B sera bientôt disponible.\n\n"
                "Cette fonctionnalité permettra de :\n"
                "• Créer des fiches T.N.B\n"
                "• Gérer les ordres de versement\n"
                "• Imprimer les documents officiels"
            )
        except Exception as e:
            import tkinter.messagebox as messagebox
            messagebox.showerror("Erreur", f"Erreur lors de l'ouverture de la fiche T.N.B: {str(e)}")
