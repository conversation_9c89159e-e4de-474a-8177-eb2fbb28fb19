from PyQt5.QtWidgets import (QW<PERSON>t, QVBoxLayout, QHBoxLayout, QLabel,
                           QLineEdit, QPushButton, QComboBox, QSpinBox,
                           QDoubleSpinBox, QFormLayout, QFrame)
from PyQt5.QtCore import Qt

class CalculatorView(QWidget):
    def __init__(self):
        super().__init__()
        layout = QVBoxLayout()
        
        # Title
        title = QLabel("TNB Tax Calculator")
        title.setStyleSheet("font-size: 20px; font-weight: bold; margin: 10px;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # Calculator form
        form_frame = QFrame()
        form_frame.setStyleSheet("""
            QFrame {
                background-color: #2C2C2C;
                border: 2px solid #666;
                border-radius: 10px;
                padding: 20px;
                margin: 10px;
            }
        """)
        
        form_layout = QFormLayout(form_frame)
        
        # Area input
        self.area_input = QDoubleSpinBox()
        self.area_input.setRange(0, 1000000)
        self.area_input.setSuffix(" m²")
        self.area_input.setStyleSheet("padding: 5px;")
        form_layout.addRow("Property Area:", self.area_input)
        
        # Zone selection
        self.zone_select = QComboBox()
        self.zone_select.addItems(["Zone 1", "Zone 2", "Zone 3"])
        self.zone_select.setStyleSheet("padding: 5px;")
        form_layout.addRow("Zone:", self.zone_select)
        
        # Property type
        self.type_select = QComboBox()
        self.type_select.addItems(["Residential", "Commercial", "Industrial"])
        self.type_select.setStyleSheet("padding: 5px;")
        form_layout.addRow("Property Type:", self.type_select)
        
        # Calculate button
        calculate_button = QPushButton("Calculate Tax")
        calculate_button.setStyleSheet("""
            QPushButton {
                padding: 10px;
                background-color: #0078D7;
                color: white;
                border-radius: 5px;
                font-weight: bold;
                margin-top: 20px;
            }
            QPushButton:hover {
                background-color: #1084E0;
            }
        """)
        calculate_button.clicked.connect(self.calculate_tax)
        form_layout.addRow("", calculate_button)
        
        layout.addWidget(form_frame)
        
        # Results section
        self.result_frame = QFrame()
        self.result_frame.setStyleSheet("""
            QFrame {
                background-color: #2C2C2C;
                border: 2px solid #666;
                border-radius: 10px;
                padding: 20px;
                margin: 10px;
            }
        """)
        result_layout = QVBoxLayout(self.result_frame)
        
        self.result_label = QLabel("Tax Amount: 0.00 MAD")
        self.result_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #0078D7;")
        self.result_label.setAlignment(Qt.AlignCenter)
        result_layout.addWidget(self.result_label)
        
        layout.addWidget(self.result_frame)
        self.setLayout(layout)
    
    def calculate_tax(self):
        # Basic tax calculation (to be improved)
        area = self.area_input.value()
        zone_rates = {"Zone 1": 20, "Zone 2": 15, "Zone 3": 10}
        type_multipliers = {"Residential": 1, "Commercial": 1.5, "Industrial": 2}
        
        base_rate = zone_rates[self.zone_select.currentText()]
        multiplier = type_multipliers[self.type_select.currentText()]
        
        tax_amount = area * base_rate * multiplier
        
        self.result_label.setText(f"Tax Amount: {tax_amount:.2f} MAD")
