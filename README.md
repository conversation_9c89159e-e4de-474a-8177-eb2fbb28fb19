# TNB Management System

## Description
TNB Management System is a desktop application for managing "Taxe sur les Terrains Non Bâtis" (TNB) in Morocco. It provides a user-friendly interface for managing taxpayers, properties, and tax payments, as well as generating reports and receipts.

## Features
- User authentication system
- Taxpayer management (add, edit, delete, search)
- Property management for each taxpayer
- Payment tracking and receipt generation
- Report generation in PDF format
- Dark mode support
- Responsive design
- French language interface

## Requirements
- Python 3.6 or higher
- ReportLab library for PDF generation

## Installation
1. Make sure you have Python installed on your system.
2. Install the required dependencies:
   ```
   pip install reportlab
   ```

## Running the Application
There are several ways to run the application:

### Method 1: Using the batch file
Double-click on the `start_tnb.bat` file to start the application.

### Method 2: Using Python directly
Open a command prompt in the project directory and run:
```
python main.py
```

### Method 3: Using Visual Studio Code
1. Open the project in Visual Studio Code.
2. Select the "Run and Debug" view from the sidebar.
3. Choose "Python: TNB Management System" from the dropdown menu.
4. Click the green play button or press F5 to start the application.

## Default Login Credentials
- Username: admin
- Password: admin

## Project Structure
```
project/
├── database/
│   ├── __init__.py
│   └── db_manager.py
├── gui/
│   ├── __init__.py
│   ├── app.py
│   ├── dashboard.py
│   ├── login.py
│   ├── payments.py
│   ├── reports.py
│   ├── settings.py
│   ├── styles.py
│   └── taxpayers.py
├── pdf_generator/
│   ├── __init__.py
│   └── generator.py
├── receipts/
│   └── (generated receipt PDFs)
├── reports/
│   └── (generated report PDFs)
├── main.py
├── start_tnb.bat
└── README.md
```

## Troubleshooting
If you encounter any issues:
1. Make sure all required dependencies are installed.
2. Check that the database file (tnb_management.db) is not corrupted.
3. Ensure that the receipts and reports directories exist and are writable.
4. If the application window doesn't appear, try running it from the command line to see any error messages.

## Alternative Command-Line Interface
If you're having trouble with the graphical interface, you can use the command-line interface:
```
python cli_app.py
```

## Development
Feel free to contribute to this project by submitting issues or pull requests.
