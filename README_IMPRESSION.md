# 📄 دليل استخدام وظيفة الطباعة بدون تسوية

## 🎯 الهدف
تم تطوير وظيفة **"Impression sans Règlement"** لإنتاج تقرير PDF مطابق للنموذج الرسمي لحالة الدفع (État de Versement).

## 🚀 كيفية الاستخدام

### 1. الوصول إلى الوظيفة
- افتح التطبيق الرئيسي
- انتقل إلى صفحة **"Ordre de Versement"** (O.V)
- املأ البيانات المطلوبة في النموذج
- اضغط على زر **"Impression sans Règlement"**

### 2. البيانات المطلوبة
يتم جمع البيانات التالية من النموذج:
- **رقم البطاقة الوطنية (CIN)**
- **اسم المكلف بالضريبة**
- **تاريخ الاقتناء**
- **تاريخ الترخيص**
- **رقم القطعة**
- **السند العقاري**
- **المساحة**
- **المنطقة**
- **التعيين**

### 3. إنتاج التقرير
عند الضغط على الزر:
1. يتم إنشاء ملف PDF تلقائياً
2. يُحفظ الملف في مجلد `output/`
3. يظهر مربع حوار للتأكيد
4. يمكن فتح الملف مباشرة

## 📋 محتوى التقرير

### العناصر الرئيسية:
- **رأس الصفحة**: معلومات الوزارة والمقاطعة
- **عنوان التقرير**: "ETAT DE VERSEMENT"
- **معلومات الوكيل**: اسم الموظف المسؤول
- **بيانات المكلف**: CIN، الاسم، العنوان، إلخ
- **تفاصيل العقار**: المساحة، السند، المنطقة
- **جدول المجورات**: تفاصيل الحسابات والزيادات
- **التوقيع**: خدمة التحصيل

### جدول المجورات يتضمن:
- السنوات
- المساحة (م²)
- التعريفة (درهم/م²)
- المبلغ الأساسي
- نسب الزيادة (15%، 10%، 5%، 0.5%)
- عدد أشهر التأخير
- إجمالي المجورات
- المبلغ الواجب الدفع
- رقم الإيصال
- تاريخ الإيصال

## 🔧 المتطلبات التقنية

### المكتبات المطلوبة:
```bash
pip install reportlab
```

### الملفات المتضمنة:
- `utils/pdf_generator.py` - مولد PDF الرئيسي
- `gui/payment_order.py` - واجهة أمر الدفع
- `test_pdf.py` - ملف اختبار

## 📁 هيكل الملفات

```
projet_capilot/
├── utils/
│   └── pdf_generator.py      # مولد PDF
├── gui/
│   └── payment_order.py      # صفحة أمر الدفع
├── output/                   # مجلد ملفات PDF
│   └── etat_versement_*.pdf  # ملفات التقارير
└── test_pdf.py              # ملف الاختبار
```

## 🎨 التخصيص

### تعديل البيانات الافتراضية:
يمكن تعديل البيانات الافتراضية في ملف `pdf_generator.py`:

```python
data = {
    'agent': 'RAHMANI HICHAM',
    'rubrique': '417.***********',
    # ... باقي البيانات
}
```

### تخصيص التصميم:
- تعديل الألوان في `colors.black`, `colors.white`
- تغيير أحجام الخطوط في `FONTSIZE`
- تعديل عرض الأعمدة في `colWidths`

## 🐛 استكشاف الأخطاء

### خطأ: "Module reportlab not found"
```bash
pip install reportlab
```

### خطأ: "Permission denied"
- تأكد من إغلاق ملف PDF المفتوح
- تحقق من صلاحيات الكتابة في مجلد `output/`

### خطأ: "File not opening"
- تأكد من وجود برنامج قارئ PDF
- جرب فتح الملف يدوياً من مجلد `output/`

## ✅ اختبار الوظيفة

لاختبار مولد PDF بشكل منفصل:
```bash
python test_pdf.py
```

## 📞 الدعم
في حالة وجود مشاكل أو أسئلة، يرجى التحقق من:
1. تثبيت المكتبات المطلوبة
2. صحة البيانات المدخلة
3. صلاحيات الملفات والمجلدات
