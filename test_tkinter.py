#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test script to verify <PERSON><PERSON><PERSON> is working correctly
"""

import tkinter as tk
from tkinter import messagebox

def main():
    """Main function to test Tkinter."""
    print("Starting Tkinter test...")
    
    # Create the main window
    root = tk.Tk()
    root.title("Tkinter Test")
    root.geometry("300x200")
    
    # Add a label
    label = tk.Label(root, text="Tkinter is working!")
    label.pack(pady=20)
    
    # Add a button
    button = tk.Button(root, text="Click Me", command=lambda: messagebox.showinfo("Info", "Button clicked!"))
    button.pack(pady=10)
    
    # Add a quit button
    quit_button = tk.But<PERSON>(root, text="Quit", command=root.destroy)
    quit_button.pack(pady=10)
    
    print("Window created, starting main loop...")
    
    # Start the main loop
    root.mainloop()
    
    print("Main loop ended.")

if __name__ == "__main__":
    main()
