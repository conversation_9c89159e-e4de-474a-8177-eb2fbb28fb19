#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Styles for TNB Management System GUI
"""

class Styles:
    """Class to manage application styles."""
    
    @staticmethod
    def get_theme(is_dark_mode=True):
        """Get the theme colors based on dark mode setting."""
        if is_dark_mode:
            return {
                "bg": "#2c2c2c",
                "fg": "#ffffff",
                "button_bg": "#0078D7",
                "button_fg": "#ffffff",
                "entry_bg": "#3c3c3c",
                "entry_fg": "#ffffff",
                "success_bg": "#28a745",
                "success_fg": "#ffffff",
                "danger_bg": "#dc3545",
                "danger_fg": "#ffffff",
                "warning_bg": "#ffc107",
                "warning_fg": "#000000",
                "info_bg": "#17a2b8",
                "info_fg": "#ffffff",
                "border": "#666666",
                "highlight_bg": "#0078D7",
                "highlight_fg": "#ffffff",
                "table_header_bg": "#444444",
                "table_header_fg": "#ffffff",
                "table_row_odd": "#333333",
                "table_row_even": "#3c3c3c",
            }
        else:
            return {
                "bg": "#f0f0f0",
                "fg": "#000000",
                "button_bg": "#0078D7",
                "button_fg": "#ffffff",
                "entry_bg": "#ffffff",
                "entry_fg": "#000000",
                "success_bg": "#28a745",
                "success_fg": "#ffffff",
                "danger_bg": "#dc3545",
                "danger_fg": "#ffffff",
                "warning_bg": "#ffc107",
                "warning_fg": "#000000",
                "info_bg": "#17a2b8",
                "info_fg": "#ffffff",
                "border": "#cccccc",
                "highlight_bg": "#0078D7",
                "highlight_fg": "#ffffff",
                "table_header_bg": "#e0e0e0",
                "table_header_fg": "#000000",
                "table_row_odd": "#f5f5f5",
                "table_row_even": "#ffffff",
            }
    
    @staticmethod
    def apply_theme_to_widget(widget, theme):
        """Apply theme to a widget."""
        widget.configure(bg=theme["bg"], fg=theme["fg"])
    
    @staticmethod
    def get_button_style(theme, button_type="default"):
        """Get button style based on theme and button type."""
        if button_type == "success":
            return {
                "bg": theme["success_bg"],
                "fg": theme["success_fg"],
                "activebackground": theme["success_bg"],
                "activeforeground": theme["success_fg"],
                "relief": "flat",
                "borderwidth": 0,
                "padx": 10,
                "pady": 5,
                "font": ("Arial", 10, "bold")
            }
        elif button_type == "danger":
            return {
                "bg": theme["danger_bg"],
                "fg": theme["danger_fg"],
                "activebackground": theme["danger_bg"],
                "activeforeground": theme["danger_fg"],
                "relief": "flat",
                "borderwidth": 0,
                "padx": 10,
                "pady": 5,
                "font": ("Arial", 10, "bold")
            }
        elif button_type == "warning":
            return {
                "bg": theme["warning_bg"],
                "fg": theme["warning_fg"],
                "activebackground": theme["warning_bg"],
                "activeforeground": theme["warning_fg"],
                "relief": "flat",
                "borderwidth": 0,
                "padx": 10,
                "pady": 5,
                "font": ("Arial", 10, "bold")
            }
        elif button_type == "info":
            return {
                "bg": theme["info_bg"],
                "fg": theme["info_fg"],
                "activebackground": theme["info_bg"],
                "activeforeground": theme["info_fg"],
                "relief": "flat",
                "borderwidth": 0,
                "padx": 10,
                "pady": 5,
                "font": ("Arial", 10, "bold")
            }
        else:  # default
            return {
                "bg": theme["button_bg"],
                "fg": theme["button_fg"],
                "activebackground": theme["button_bg"],
                "activeforeground": theme["button_fg"],
                "relief": "flat",
                "borderwidth": 0,
                "padx": 10,
                "pady": 5,
                "font": ("Arial", 10, "bold")
            }
    
    @staticmethod
    def get_entry_style(theme):
        """Get entry style based on theme."""
        return {
            "bg": theme["entry_bg"],
            "fg": theme["entry_fg"],
            "insertbackground": theme["fg"],  # cursor color
            "relief": "flat",
            "borderwidth": 1,
            "highlightthickness": 1,
            "highlightbackground": theme["border"],
            "highlightcolor": theme["highlight_bg"],
            "font": ("Arial", 10)
        }
    
    @staticmethod
    def get_label_style(theme, size="normal", weight="normal"):
        """Get label style based on theme and size."""
        font_sizes = {
            "small": 8,
            "normal": 10,
            "large": 12,
            "title": 16,
            "header": 20
        }
        
        font_weights = {
            "normal": "normal",
            "bold": "bold"
        }
        
        return {
            "bg": theme["bg"],
            "fg": theme["fg"],
            "font": ("Arial", font_sizes.get(size, 10), font_weights.get(weight, "normal"))
        }
    
    @staticmethod
    def get_frame_style(theme):
        """Get frame style based on theme."""
        return {
            "bg": theme["bg"],
            "highlightthickness": 1,
            "highlightbackground": theme["border"],
            "padx": 10,
            "pady": 10
        }
    
    @staticmethod
    def get_listbox_style(theme):
        """Get listbox style based on theme."""
        return {
            "bg": theme["entry_bg"],
            "fg": theme["entry_fg"],
            "selectbackground": theme["highlight_bg"],
            "selectforeground": theme["highlight_fg"],
            "relief": "flat",
            "borderwidth": 1,
            "highlightthickness": 1,
            "highlightbackground": theme["border"],
            "highlightcolor": theme["highlight_bg"],
            "font": ("Arial", 10)
        }
    
    @staticmethod
    def get_treeview_style(theme):
        """Get treeview style based on theme."""
        return {
            "bg": theme["entry_bg"],
            "fg": theme["entry_fg"],
            "fieldbackground": theme["entry_bg"],
            "font": ("Arial", 10),
            "rowheight": 25,
            "borderwidth": 0,
            "relief": "flat"
        }
