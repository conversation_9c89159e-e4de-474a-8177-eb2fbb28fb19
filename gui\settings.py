#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Settings Frame for TNB Management System
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import os
from .styles import Styles

class SettingsFrame(tk.Frame):
    def __init__(self, parent, app):
        """Initialize the settings frame."""
        self.app = app
        self.theme = app.theme
        
        super().__init__(parent, bg=self.theme["bg"])
        
        # Create widgets
        self.create_widgets()
    
    def create_widgets(self):
        """Create the settings frame widgets."""
        # Main container with padding
        main_container = tk.Frame(self, bg=self.theme["bg"], padx=20, pady=20)
        main_container.pack(fill=tk.BOTH, expand=True)
        
        # Title
        title_label = tk.Label(
            main_container,
            text="Paramètres",
            **Styles.get_label_style(self.theme, size="title", weight="bold")
        )
        title_label.pack(anchor=tk.W, pady=(0, 20))
        
        # Appearance settings
        appearance_frame = tk.LabelFrame(
            main_container,
            text="Apparence",
            bg=self.theme["bg"],
            fg=self.theme["fg"],
            font=("Arial", 12, "bold"),
            padx=10,
            pady=10
        )
        appearance_frame.pack(fill=tk.X, pady=10)
        
        # Theme selection
        theme_frame = tk.Frame(appearance_frame, bg=self.theme["bg"])
        theme_frame.pack(fill=tk.X, pady=5)
        
        theme_label = tk.Label(
            theme_frame,
            text="Thème:",
            **Styles.get_label_style(self.theme)
        )
        theme_label.pack(side=tk.LEFT, padx=5)
        
        self.theme_var = tk.StringVar(value="dark" if self.app.is_dark_mode else "light")
        
        dark_radio = tk.Radiobutton(
            theme_frame,
            text="Sombre",
            variable=self.theme_var,
            value="dark",
            command=self.on_theme_changed,
            bg=self.theme["bg"],
            fg=self.theme["fg"],
            selectcolor=self.theme["bg"],
            activebackground=self.theme["bg"],
            activeforeground=self.theme["fg"]
        )
        dark_radio.pack(side=tk.LEFT, padx=5)
        
        light_radio = tk.Radiobutton(
            theme_frame,
            text="Clair",
            variable=self.theme_var,
            value="light",
            command=self.on_theme_changed,
            bg=self.theme["bg"],
            fg=self.theme["fg"],
            selectcolor=self.theme["bg"],
            activebackground=self.theme["bg"],
            activeforeground=self.theme["fg"]
        )
        light_radio.pack(side=tk.LEFT, padx=5)
        
        # Font size
        font_frame = tk.Frame(appearance_frame, bg=self.theme["bg"])
        font_frame.pack(fill=tk.X, pady=5)
        
        font_label = tk.Label(
            font_frame,
            text="Taille de Police:",
            **Styles.get_label_style(self.theme)
        )
        font_label.pack(side=tk.LEFT, padx=5)
        
        self.font_var = tk.StringVar(value="medium")
        
        small_radio = tk.Radiobutton(
            font_frame,
            text="Petite",
            variable=self.font_var,
            value="small",
            bg=self.theme["bg"],
            fg=self.theme["fg"],
            selectcolor=self.theme["bg"],
            activebackground=self.theme["bg"],
            activeforeground=self.theme["fg"]
        )
        small_radio.pack(side=tk.LEFT, padx=5)
        
        medium_radio = tk.Radiobutton(
            font_frame,
            text="Moyenne",
            variable=self.font_var,
            value="medium",
            bg=self.theme["bg"],
            fg=self.theme["fg"],
            selectcolor=self.theme["bg"],
            activebackground=self.theme["bg"],
            activeforeground=self.theme["fg"]
        )
        medium_radio.pack(side=tk.LEFT, padx=5)
        
        large_radio = tk.Radiobutton(
            font_frame,
            text="Grande",
            variable=self.font_var,
            value="large",
            bg=self.theme["bg"],
            fg=self.theme["fg"],
            selectcolor=self.theme["bg"],
            activebackground=self.theme["bg"],
            activeforeground=self.theme["fg"]
        )
        large_radio.pack(side=tk.LEFT, padx=5)
        
        # Application settings
        app_frame = tk.LabelFrame(
            main_container,
            text="Application",
            bg=self.theme["bg"],
            fg=self.theme["fg"],
            font=("Arial", 12, "bold"),
            padx=10,
            pady=10
        )
        app_frame.pack(fill=tk.X, pady=10)
        
        # Database path
        db_frame = tk.Frame(app_frame, bg=self.theme["bg"])
        db_frame.pack(fill=tk.X, pady=5)
        
        db_label = tk.Label(
            db_frame,
            text="Base de Données:",
            **Styles.get_label_style(self.theme)
        )
        db_label.pack(side=tk.LEFT, padx=5)
        
        self.db_entry = tk.Entry(
            db_frame,
            width=40,
            **Styles.get_entry_style(self.theme)
        )
        self.db_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        self.db_entry.insert(0, "tnb_management.db")
        
        db_browse_button = tk.Button(
            db_frame,
            text="Parcourir",
            command=self.browse_db_path,
            **Styles.get_button_style(self.theme)
        )
        db_browse_button.pack(side=tk.LEFT, padx=5)
        
        # Reports output path
        reports_frame = tk.Frame(app_frame, bg=self.theme["bg"])
        reports_frame.pack(fill=tk.X, pady=5)
        
        reports_label = tk.Label(
            reports_frame,
            text="Dossier Rapports:",
            **Styles.get_label_style(self.theme)
        )
        reports_label.pack(side=tk.LEFT, padx=5)
        
        self.reports_entry = tk.Entry(
            reports_frame,
            width=40,
            **Styles.get_entry_style(self.theme)
        )
        self.reports_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        self.reports_entry.insert(0, os.path.join(os.getcwd(), "reports"))
        
        reports_browse_button = tk.Button(
            reports_frame,
            text="Parcourir",
            command=self.browse_reports_path,
            **Styles.get_button_style(self.theme)
        )
        reports_browse_button.pack(side=tk.LEFT, padx=5)
        
        # Receipts output path
        receipts_frame = tk.Frame(app_frame, bg=self.theme["bg"])
        receipts_frame.pack(fill=tk.X, pady=5)
        
        receipts_label = tk.Label(
            receipts_frame,
            text="Dossier Reçus:",
            **Styles.get_label_style(self.theme)
        )
        receipts_label.pack(side=tk.LEFT, padx=5)
        
        self.receipts_entry = tk.Entry(
            receipts_frame,
            width=40,
            **Styles.get_entry_style(self.theme)
        )
        self.receipts_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        self.receipts_entry.insert(0, os.path.join(os.getcwd(), "receipts"))
        
        receipts_browse_button = tk.Button(
            receipts_frame,
            text="Parcourir",
            command=self.browse_receipts_path,
            **Styles.get_button_style(self.theme)
        )
        receipts_browse_button.pack(side=tk.LEFT, padx=5)
        
        # User management
        user_frame = tk.LabelFrame(
            main_container,
            text="Gestion des Utilisateurs",
            bg=self.theme["bg"],
            fg=self.theme["fg"],
            font=("Arial", 12, "bold"),
            padx=10,
            pady=10
        )
        user_frame.pack(fill=tk.X, pady=10)
        
        # User management buttons
        user_buttons_frame = tk.Frame(user_frame, bg=self.theme["bg"])
        user_buttons_frame.pack(fill=tk.X, pady=5)
        
        change_password_button = tk.Button(
            user_buttons_frame,
            text="Changer le Mot de Passe",
            command=self.change_password,
            **Styles.get_button_style(self.theme)
        )
        change_password_button.pack(side=tk.LEFT, padx=5)
        
        manage_users_button = tk.Button(
            user_buttons_frame,
            text="Gérer les Utilisateurs",
            command=self.manage_users,
            **Styles.get_button_style(self.theme)
        )
        manage_users_button.pack(side=tk.LEFT, padx=5)
        
        # Save settings button
        button_frame = tk.Frame(main_container, bg=self.theme["bg"])
        button_frame.pack(fill=tk.X, pady=20)
        
        save_button = tk.Button(
            button_frame,
            text="Enregistrer les Paramètres",
            command=self.save_settings,
            **Styles.get_button_style(self.theme, "success")
        )
        save_button.pack(side=tk.RIGHT, padx=5)
    
    def update_theme(self, theme):
        """Update the theme of all widgets."""
        self.theme = theme
        self.configure(bg=theme["bg"])
        
        # Update all child widgets (simplified for brevity)
        for widget in self.winfo_children():
            if isinstance(widget, tk.Frame):
                widget.configure(bg=theme["bg"])
    
    def on_theme_changed(self):
        """Handle theme change event."""
        new_theme = self.theme_var.get()
        if (new_theme == "dark" and not self.app.is_dark_mode) or (new_theme == "light" and self.app.is_dark_mode):
            self.app.toggle_theme()
    
    def browse_db_path(self):
        """Browse for database file."""
        file_path = filedialog.askopenfilename(
            initialdir=os.path.dirname(self.db_entry.get()),
            title="Sélectionner la Base de Données",
            filetypes=(("SQLite Database", "*.db"), ("All Files", "*.*"))
        )
        if file_path:
            self.db_entry.delete(0, tk.END)
            self.db_entry.insert(0, file_path)
    
    def browse_reports_path(self):
        """Browse for reports directory."""
        directory = filedialog.askdirectory(initialdir=self.reports_entry.get())
        if directory:
            self.reports_entry.delete(0, tk.END)
            self.reports_entry.insert(0, directory)
    
    def browse_receipts_path(self):
        """Browse for receipts directory."""
        directory = filedialog.askdirectory(initialdir=self.receipts_entry.get())
        if directory:
            self.receipts_entry.delete(0, tk.END)
            self.receipts_entry.insert(0, directory)
    
    def change_password(self):
        """Show dialog to change password."""
        # This would be implemented to show a password change dialog
        messagebox.showinfo("Changer le Mot de Passe", "Fonctionnalité à implémenter.")
    
    def manage_users(self):
        """Show dialog to manage users."""
        # This would be implemented to show a user management dialog
        messagebox.showinfo("Gérer les Utilisateurs", "Fonctionnalité à implémenter.")
    
    def save_settings(self):
        """Save the application settings."""
        # This would be implemented to save settings to a configuration file
        messagebox.showinfo("Paramètres", "Paramètres enregistrés avec succès.")
