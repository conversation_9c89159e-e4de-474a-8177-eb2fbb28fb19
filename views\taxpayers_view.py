from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                           QLabel, QLineEdit, QTableWidget, QTableWidgetItem,
                           QHeaderView)
from PyQt5.QtCore import Qt

class TaxpayersView(QWidget):
    def __init__(self):
        super().__init__()
        layout = QVBoxLayout()
        
        # Search bar
        search_layout = QHBoxLayout()
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Search taxpayers...")
        self.search_input.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 2px solid #666;
                border-radius: 5px;
                margin: 5px;
            }
        """)
        search_layout.addWidget(self.search_input)
        
        search_button = QPushButton("Search")
        search_button.setStyleSheet("""
            QPushButton {
                padding: 8px 20px;
                background-color: #0078D7;
                color: white;
                border-radius: 5px;
                margin: 5px;
            }
        """)
        search_layout.addWidget(search_button)
        
        add_button = QPushButton("Add New")
        add_button.setStyleSheet("""
            QPushButton {
                padding: 8px 20px;
                background-color: #28a745;
                color: white;
                border-radius: 5px;
                margin: 5px;
            }
        """)
        search_layout.addWidget(add_button)
        
        layout.addLayout(search_layout)
        
        # Taxpayers table
        self.table = QTableWidget()
        self.table.setColumnCount(5)
        self.table.setHorizontalHeaderLabels([
            "Name", "National ID", "Address", "Properties", "Total Tax"
        ])
        self.table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.table.setStyleSheet("""
            QTableWidget {
                border: 2px solid #666;
                border-radius: 5px;
                margin: 5px;
            }
            QHeaderView::section {
                background-color: #2C2C2C;
                padding: 8px;
                border: 1px solid #666;
            }
        """)
        
        layout.addWidget(self.table)
        
        self.setLayout(layout)
