#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Property Details Frame for TNB Management System
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
import os
from .styles import Styles

class PropertyDetailsFrame(tk.Frame):
    def __init__(self, parent, app, property_id=None):
        """Initialize the property details frame."""
        self.app = app
        self.theme = app.theme
        self.property_id = property_id

        # Initialize variables for property data
        self.property_data = None
        self.taxpayer_data = None
        self.tax_history = []
        self.remaining_payments = []

        super().__init__(parent, bg=self.theme["bg"])

        # Create widgets
        self.create_widgets()

        # Load property data if ID is provided
        if property_id:
            self.load_property_data(property_id)

    def create_widgets(self):
        """Create the property details widgets."""
        # Main container with padding
        main_container = tk.Frame(self, bg=self.theme["bg"], padx=20, pady=20)
        main_container.pack(fill=tk.BOTH, expand=True)

        # Header with property ID
        header_frame = tk.Frame(main_container, bg=self.theme["bg"])
        header_frame.pack(fill=tk.X, pady=(0, 20))

        self.title_label = tk.Label(
            header_frame,
            text="Fiche T.N.B",
            **Styles.get_label_style(self.theme, size="title", weight="bold")
        )
        self.title_label.pack(side=tk.LEFT)

        # Property ID display
        id_frame = tk.Frame(header_frame, bg=self.theme["bg"])
        id_frame.pack(side=tk.RIGHT)

        self.property_id_label = tk.Label(
            id_frame,
            text="N°: ",
            **Styles.get_label_style(self.theme, size="large")
        )
        self.property_id_label.pack(side=tk.LEFT)

        self.property_id_value = tk.Label(
            id_frame,
            text="",
            **Styles.get_label_style(self.theme, size="large", weight="bold")
        )
        self.property_id_value.pack(side=tk.LEFT)

        # Property details section
        details_frame = tk.LabelFrame(
            main_container,
            text="Redevables",
            bg=self.theme["bg"],
            fg=self.theme["fg"],
            font=("Arial", 12, "bold")
        )
        details_frame.pack(fill=tk.X, pady=10)

        # Create grid for property details
        self.create_property_details_grid(details_frame)

        # Tax history section
        history_frame = tk.LabelFrame(
            main_container,
            text="Historique T.N.B",
            bg=self.theme["bg"],
            fg=self.theme["fg"],
            font=("Arial", 12, "bold")
        )
        history_frame.pack(fill=tk.BOTH, expand=True, pady=10)

        # Create tax history table
        self.create_tax_history_table(history_frame)

        # Remaining payments section
        remaining_frame = tk.LabelFrame(
            main_container,
            text="Reste à payer",
            bg=self.theme["bg"],
            fg=self.theme["fg"],
            font=("Arial", 12, "bold")
        )
        remaining_frame.pack(fill=tk.X, pady=10)

        # Create remaining payments table
        self.create_remaining_payments_table(remaining_frame)

        # Buttons frame
        buttons_frame = tk.Frame(main_container, bg=self.theme["bg"])
        buttons_frame.pack(fill=tk.X, pady=10)

        # Add O.V button (Order Voucher)
        ov_button = tk.Button(
            buttons_frame,
            text="O.V",
            command=self.open_payment_order,
            **Styles.get_button_style(self.theme, button_type="info")
        )
        ov_button.pack(side=tk.LEFT, padx=5)

        print_button = tk.Button(
            buttons_frame,
            text="Impression Avec Règlement",
            command=self.print_with_regulation,
            **Styles.get_button_style(self.theme)
        )
        print_button.pack(side=tk.RIGHT, padx=5)

        print_without_button = tk.Button(
            buttons_frame,
            text="Impression Sans Règlement",
            command=self.print_without_regulation,
            **Styles.get_button_style(self.theme)
        )
        print_without_button.pack(side=tk.RIGHT, padx=5)

    def create_property_details_grid(self, parent):
        """Create the grid for property details."""
        # Create a frame for the grid
        grid_frame = tk.Frame(parent, bg=self.theme["bg"], padx=10, pady=10)
        grid_frame.pack(fill=tk.BOTH, expand=True)

        # Define field labels and entry widgets
        fields = [
            # Row 1
            {"label": "Secteur", "row": 0, "column": 0, "var_name": "sector"},
            {"label": "Zone", "row": 0, "column": 2, "var_name": "zone"},
            {"label": "Sup. C", "row": 0, "column": 4, "var_name": "area", "suffix": "m²"},

            # Row 2
            {"label": "N° Lot", "row": 1, "column": 0, "var_name": "lot_number"},
            {"label": "T.F", "row": 1, "column": 2, "var_name": "tf_number"},
            {"label": "Date d'Acquisition", "row": 1, "column": 4, "var_name": "acquisition_date", "is_date": True},

            # Row 3
            {"label": "Désignation", "row": 2, "column": 0, "var_name": "designation", "span": 3},
            {"label": "Date Permis d'Habiter", "row": 2, "column": 4, "var_name": "habitation_permit_date", "is_date": True},

            # Row 4
            {"label": "Date d'Autorisat° Const.", "row": 3, "column": 0, "var_name": "construction_auth_date", "is_date": True},
            {"label": "N° Autorisat° Const.", "row": 3, "column": 2, "var_name": "construction_auth_number"},
            {"label": "Date Déclarat° vente", "row": 3, "column": 4, "var_name": "sale_declaration_date", "is_date": True},

            # Row 5
            {"label": "N° Permis d'Habiter", "row": 4, "column": 0, "var_name": "habitation_permit_number"},
            {"label": "Date de Vente", "row": 4, "column": 2, "var_name": "sale_date", "is_date": True},
            {"label": "Année non comptabilisée", "row": 4, "column": 4, "var_name": "non_accounted_year"},

            # Row 6
            {"label": "Nbr Jours", "row": 5, "column": 0, "var_name": "days_count"},
            {"label": "Majorat°", "row": 5, "column": 2, "var_name": "majoration"},
            {"label": "N° Quitt°", "row": 5, "column": 3, "var_name": "receipt_number"},
            {"label": "Date Quitt°", "row": 5, "column": 4, "var_name": "receipt_date", "is_date": True}
        ]

        # Store entry widgets in a dictionary for later access
        self.entries = {}

        # Create labels and entry widgets
        for field in fields:
            # Create label
            label = tk.Label(
                grid_frame,
                text=field["label"],
                **Styles.get_label_style(self.theme)
            )
            label.grid(row=field["row"], column=field["column"], sticky="w", padx=5, pady=5)

            # Create entry widget
            entry_column = field["column"] + 1
            columnspan = field.get("span", 1)

            entry = tk.Entry(
                grid_frame,
                **Styles.get_entry_style(self.theme)
            )
            entry.grid(row=field["row"], column=entry_column, columnspan=columnspan, sticky="ew", padx=5, pady=5)

            # Store entry widget
            self.entries[field["var_name"]] = entry

            # Add suffix label if specified
            if "suffix" in field:
                suffix_label = tk.Label(
                    grid_frame,
                    text=field["suffix"],
                    **Styles.get_label_style(self.theme)
                )
                suffix_label.grid(row=field["row"], column=entry_column + columnspan, sticky="w", padx=2, pady=5)

        # Configure grid columns to expand
        for i in range(6):
            grid_frame.columnconfigure(i, weight=1)

        # Add a "New" button for adding new taxpayers
        new_button = tk.Button(
            grid_frame,
            text="Nouveau",
            command=self.add_new_taxpayer,
            **Styles.get_button_style(self.theme, button_type="success")
        )
        new_button.grid(row=0, column=6, padx=5, pady=5, sticky="e")

        # Add a date picker button for date fields
        for field in fields:
            if field.get("is_date", False):
                date_button = tk.Button(
                    grid_frame,
                    text="...",
                    command=lambda f=field["var_name"]: self.show_date_picker(f),
                    width=2,
                    **Styles.get_button_style(self.theme)
                )
                date_button.grid(row=field["row"], column=field["column"] + 2, padx=0, pady=5, sticky="w")

    def create_tax_history_table(self, parent):
        """Create the tax history table."""
        # Create a frame for the table
        table_frame = tk.Frame(parent, bg=self.theme["bg"], padx=10, pady=10)
        table_frame.pack(fill=tk.BOTH, expand=True)

        # Create scrollbars
        y_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL)
        y_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        x_scrollbar = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL)
        x_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)

        # Define columns
        columns = (
            "year", "article", "ov_number", "ov_date", "receipt_number",
            "receipt_date", "amount", "observations"
        )

        # Create treeview
        self.history_tree = ttk.Treeview(
            table_frame,
            columns=columns,
            show="headings",
            yscrollcommand=y_scrollbar.set,
            xscrollcommand=x_scrollbar.set
        )

        # Configure scrollbars
        y_scrollbar.config(command=self.history_tree.yview)
        x_scrollbar.config(command=self.history_tree.xview)

        # Define column headings
        column_headings = {
            "year": "Année",
            "article": "Article",
            "ov_number": "N° OV",
            "ov_date": "Date OV",
            "receipt_number": "N° Quitt",
            "receipt_date": "Date Quitt",
            "amount": "Montant",
            "observations": "Observations"
        }

        # Set column headings and widths
        for col in columns:
            self.history_tree.heading(col, text=column_headings[col])
            self.history_tree.column(col, width=100, minwidth=80)

        # Adjust specific column widths
        self.history_tree.column("observations", width=200, minwidth=150)

        # Pack the treeview
        self.history_tree.pack(fill=tk.BOTH, expand=True)

        # Add a total row at the bottom
        total_frame = tk.Frame(table_frame, bg=self.theme["bg"])
        total_frame.pack(fill=tk.X, pady=(5, 0))

        total_label = tk.Label(
            total_frame,
            text="TOTAL",
            **Styles.get_label_style(self.theme, weight="bold")
        )
        total_label.pack(side=tk.LEFT, padx=5)

        self.total_amount = tk.Label(
            total_frame,
            text="0.00",
            **Styles.get_label_style(self.theme, weight="bold")
        )
        self.total_amount.pack(side=tk.RIGHT, padx=5)

    def create_remaining_payments_table(self, parent):
        """Create the remaining payments table."""
        # Create a frame for the table
        table_frame = tk.Frame(parent, bg=self.theme["bg"], padx=10, pady=10)
        table_frame.pack(fill=tk.BOTH, expand=True)

        # Create scrollbars
        y_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL)
        y_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        x_scrollbar = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL)
        x_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)

        # Define columns
        columns = (
            "year", "area", "rate", "principal_amount",
            "majoration_15", "majoration_10", "majoration_5",
            "months", "majoration_05", "total_amount"
        )

        # Create treeview
        self.remaining_tree = ttk.Treeview(
            table_frame,
            columns=columns,
            show="headings",
            yscrollcommand=y_scrollbar.set,
            xscrollcommand=x_scrollbar.set
        )

        # Configure scrollbars
        y_scrollbar.config(command=self.remaining_tree.yview)
        x_scrollbar.config(command=self.remaining_tree.xview)

        # Define column headings
        column_headings = {
            "year": "Année",
            "area": "Superficie m²",
            "rate": "Tarif DH/m²",
            "principal_amount": "Montant Principal",
            "majoration_15": "Majoration 15%",
            "majoration_10": "Majoration 10%",
            "majoration_5": "Majoration 5%",
            "months": "Nbr de Mois",
            "majoration_05": "Majoration 0.5%",
            "total_amount": "Montant à payer"
        }

        # Set column headings and widths
        for col in columns:
            self.remaining_tree.heading(col, text=column_headings[col])
            self.remaining_tree.column(col, width=100, minwidth=80)

        # Pack the treeview
        self.remaining_tree.pack(fill=tk.BOTH, expand=True)

        # Add a total row at the bottom
        total_frame = tk.Frame(table_frame, bg=self.theme["bg"])
        total_frame.pack(fill=tk.X, pady=(5, 0))

        total_label = tk.Label(
            total_frame,
            text="TOTAL",
            **Styles.get_label_style(self.theme, weight="bold")
        )
        total_label.pack(side=tk.LEFT, padx=5)

        self.remaining_total = tk.Label(
            total_frame,
            text="0.00",
            **Styles.get_label_style(self.theme, weight="bold")
        )
        self.remaining_total.pack(side=tk.RIGHT, padx=5)

    def load_property_data(self, property_id):
        """Load property data from the database."""
        try:
            # Set property ID in the header
            self.property_id_value.config(text=str(property_id))

            # Get property data from database
            property_data = self.app.db_manager.get_property_by_id(property_id)
            if not property_data:
                messagebox.showerror("Erreur", f"Propriété avec ID {property_id} non trouvée.")
                return

            self.property_data = property_data

            # Get taxpayer data
            taxpayer_id = property_data.get("taxpayer_id")
            if taxpayer_id:
                self.taxpayer_data = self.app.db_manager.get_taxpayer_by_id(taxpayer_id)

            # Fill in property details
            self.populate_property_details()

            # Load tax history
            self.load_tax_history()

            # Calculate remaining payments
            self.calculate_remaining_payments()

        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du chargement des données: {str(e)}")

    def populate_property_details(self):
        """Populate the property details form with data."""
        if not self.property_data:
            return

        # Map property data to form fields
        field_mapping = {
            "sector": self.property_data.get("zone", ""),
            "zone": self.property_data.get("category", ""),
            "area": str(self.property_data.get("area", "")),
            "lot_number": self.property_data.get("reference", ""),
            "designation": self.property_data.get("address", "")
        }

        # Fill in the fields
        for field, value in field_mapping.items():
            if field in self.entries and value is not None:
                self.entries[field].delete(0, tk.END)
                self.entries[field].insert(0, value)

    def load_tax_history(self):
        """Load tax payment history from the database."""
        if not self.property_data or not self.taxpayer_data:
            return

        # Clear existing data
        for item in self.history_tree.get_children():
            self.history_tree.delete(item)

        # Get payment history from database
        payments = self.app.db_manager.get_payments_by_property(self.property_data["id"])
        self.tax_history = payments

        # Insert data into treeview
        total = 0
        for payment in payments:
            # Format date
            payment_date = payment.get("payment_date", "")
            if payment_date and isinstance(payment_date, str):
                try:
                    date_obj = datetime.strptime(payment_date, "%Y-%m-%d %H:%M:%S")
                    payment_date = date_obj.strftime("%d-%m-%Y")
                except:
                    pass

            # Insert row
            self.history_tree.insert("", "end", values=(
                payment.get("year", ""),
                "",  # Article
                payment.get("receipt_number", ""),  # Using receipt number as OV number
                "",  # OV date
                payment.get("receipt_number", ""),
                payment_date,
                f"{payment.get('amount', 0):.2f}",
                payment.get("notes", "")
            ))

            # Add to total
            total += float(payment.get("amount", 0))

        # Update total
        self.total_amount.config(text=f"{total:.2f}")

    def calculate_remaining_payments(self):
        """Calculate remaining payments based on property data and payment history."""
        if not self.property_data:
            return

        # Clear existing data
        for item in self.remaining_tree.get_children():
            self.remaining_tree.delete(item)

        # Get current year
        current_year = datetime.now().year

        # Get paid years from history
        paid_years = set()
        for payment in self.tax_history:
            paid_years.add(payment.get("year"))

        # Calculate for the last 4 years if not paid
        years_to_calculate = []
        for year in range(current_year - 4, current_year + 1):
            if year not in paid_years:
                years_to_calculate.append(year)

        # Get property area and rate
        area = float(self.property_data.get("area", 0))

        # Determine rate based on zone
        zone = self.property_data.get("zone", "").upper()
        if "NON EQUIPE" in zone:
            rate = 4  # Example rate for non-equipped zone
        else:
            rate = 6  # Example rate for equipped zone

        # Calculate for each year
        total_remaining = 0
        for year in years_to_calculate:
            # Calculate principal amount
            principal = area * rate

            # Calculate years of delay
            years_delay = current_year - year

            # Calculate majorations
            majoration_15 = principal * 0.15 if years_delay > 0 else 0
            majoration_10 = principal * 0.10 if years_delay > 1 else 0
            majoration_5 = principal * 0.05 if years_delay > 2 else 0

            # Calculate months of delay in current year
            current_month = datetime.now().month
            months_delay = current_month if years_delay == 0 else 12

            # Calculate monthly majoration (0.5% per month)
            majoration_05 = principal * 0.005 * months_delay

            # Calculate total for this year
            year_total = principal + majoration_15 + majoration_10 + majoration_5 + majoration_05

            # Insert into treeview
            self.remaining_tree.insert("", "end", values=(
                year,
                f"{area:.2f}",
                f"{rate:.2f}",
                f"{principal:.2f}",
                f"{majoration_15:.2f}",
                f"{majoration_10:.2f}",
                f"{majoration_5:.2f}",
                months_delay,
                f"{majoration_05:.2f}",
                f"{year_total:.2f}"
            ))

            # Add to total
            total_remaining += year_total

        # Update total
        self.remaining_total.config(text=f"{total_remaining:.2f}")

    def show_date_picker(self, field_name):
        """Show date picker dialog for a date field."""
        # This would be implemented with a date picker dialog
        # For now, just show a message
        messagebox.showinfo("Date Picker", f"Date picker for {field_name} would be shown here.")

    def add_new_taxpayer(self):
        """Show dialog to add a new taxpayer."""
        # This would open a dialog to add a new taxpayer
        # For now, just show a message
        messagebox.showinfo("Nouveau Redevable", "Dialog to add a new taxpayer would be shown here.")

    def open_payment_order(self):
        """Open the payment order dialog."""
        try:
            # Check if property data is loaded
            if not self.property_data or not self.taxpayer_data:
                messagebox.showerror("Erreur", "Veuillez d'abord charger les données de la propriété.")
                return

            # Import here to avoid circular imports
            from .payment_order import PaymentOrderDialog

            # Create payment order dialog
            order_dialog = PaymentOrderDialog(
                self,
                self.app,
                taxpayer_id=self.taxpayer_data["id"],
                property_id=self.property_data["id"]
            )

            # Wait for dialog to close
            self.wait_window(order_dialog)

            # Refresh data after dialog closes
            self.load_tax_history()
            self.calculate_remaining_payments()

        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de l'ouverture de l'ordre de versement: {str(e)}")

    def print_with_regulation(self):
        """Print property details with regulation."""
        try:
            # Check if property data is loaded
            if not self.property_data or not self.taxpayer_data:
                messagebox.showerror("Erreur", "Veuillez d'abord charger les données de la propriété.")
                return

            # Prepare data for PDF generation
            report_data = {
                "property": self.property_data,
                "taxpayer": self.taxpayer_data,
                "tax_history": self.tax_history,
                "remaining_payments": self.get_remaining_payments_data(),
                "with_regulation": True
            }

            # Generate PDF
            filename = self.app.pdf_generator.generate_property_report(report_data)

            # Show success message
            messagebox.showinfo("Succès", f"Rapport généré avec succès: {filename}")

            # Open PDF
            os.startfile(filename)

        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de la génération du rapport: {str(e)}")

    def print_without_regulation(self):
        """Print property details without regulation."""
        try:
            # Check if property data is loaded
            if not self.property_data or not self.taxpayer_data:
                messagebox.showerror("Erreur", "Veuillez d'abord charger les données de la propriété.")
                return

            # Prepare data for PDF generation
            report_data = {
                "property": self.property_data,
                "taxpayer": self.taxpayer_data,
                "tax_history": self.tax_history,
                "remaining_payments": self.get_remaining_payments_data(),
                "with_regulation": False
            }

            # Generate PDF
            filename = self.app.pdf_generator.generate_property_report(report_data)

            # Show success message
            messagebox.showinfo("Succès", f"Rapport généré avec succès: {filename}")

            # Open PDF
            os.startfile(filename)

        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de la génération du rapport: {str(e)}")

    def get_remaining_payments_data(self):
        """Get remaining payments data from the treeview."""
        data = []
        for item_id in self.remaining_tree.get_children():
            values = self.remaining_tree.item(item_id, "values")
            if values:
                data.append({
                    "year": values[0],
                    "area": values[1],
                    "rate": values[2],
                    "principal_amount": values[3],
                    "majoration_15": values[4],
                    "majoration_10": values[5],
                    "majoration_5": values[6],
                    "months": values[7],
                    "majoration_05": values[8],
                    "total_amount": values[9]
                })
        return data