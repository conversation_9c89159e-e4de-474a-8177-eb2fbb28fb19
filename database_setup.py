import sqlite3

def setup_database():
    conn = sqlite3.connect("tnb_management.db")
    cursor = conn.cursor()

    # Create Users table
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS Users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            password TEXT NOT NULL
        )
    """)

    # Create Taxpayers table
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS Taxpayers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            national_id TEXT UNIQUE NOT NULL,
            address TEXT,
            email TEXT
        )
    """)

    # Insert default admin user
    cursor.execute("""
        INSERT OR IGNORE INTO Users (username, password) VALUES (?, ?)
    """, ("admin", "admin"))

    conn.commit()
    conn.close()

if __name__ == "__main__":
    setup_database()
    print("Database setup complete.")
