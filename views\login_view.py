from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QLabel, QLineEdit,
                           QPushButton, QMessageBox)
from PyQt5.QtCore import Qt

class LoginView(QDialog):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Login - TNB Management System")
        self.setFixedSize(400, 300)
        self.setWindowFlags(Qt.WindowStaysOnTopHint)
        
        layout = QVBoxLayout()
        
        # Add title
        title = QLabel("TNB Management System")
        title.setStyleSheet("font-size: 20px; font-weight: bold; margin: 20px;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # Username input
        self.username = QLineEdit()
        self.username.setPlaceholderText("Username")
        self.username.setStyleSheet("""
            QLineEdit {
                padding: 10px;
                border: 2px solid #666;
                border-radius: 5px;
                margin: 5px;
            }
        """)
        layout.addWidget(self.username)
        
        # Password input
        self.password = QLineEdit()
        self.password.setPlaceholderText("Password")
        self.password.setEchoMode(QLineEdit.Password)
        self.password.setStyleSheet("""
            QLineEdit {
                padding: 10px;
                border: 2px solid #666;
                border-radius: 5px;
                margin: 5px;
            }
        """)
        layout.addWidget(self.password)
        
        # Login button
        login_button = QPushButton("Login")
        login_button.setStyleSheet("""
            QPushButton {
                padding: 10px;
                background-color: #0078D7;
                color: white;
                border-radius: 5px;
                font-weight: bold;
                margin: 10px;
            }
            QPushButton:hover {
                background-color: #1084E0;
            }
        """)
        login_button.clicked.connect(self.try_login)
        layout.addWidget(login_button)
        
        self.setLayout(layout)
    
    def try_login(self):
        if self.username.text() == "admin" and self.password.text() == "admin":
            self.accept()
        else:
            QMessageBox.warning(self, "Error", "Invalid username or password")
