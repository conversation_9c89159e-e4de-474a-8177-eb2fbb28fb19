from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QLabel, QGridLayout,
                           QFrame)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

class DashboardView(QWidget):
    def __init__(self):
        super().__init__()
        layout = QVBoxLayout()
        
        # Add welcome message
        welcome = QLabel("Welcome to TNB Management System")
        welcome.setFont(QFont("Arial", 24))
        welcome.setAlignment(Qt.AlignCenter)
        layout.addWidget(welcome)
        
        # Create grid layout for dashboard widgets
        grid = QGridLayout()
        
        # Add dashboard widgets
        stats = [
            ("Total Taxpayers", "0"),
            ("Total Tax Collected", "234,567 DH"),
            ("Pending Payments", "156"),
            ("Active Properties", "78")
        ]
        
        for i, (title, value) in enumerate(stats):
            widget = self.create_stat_widget(title, value)
            grid.addWidget(widget, i // 2, i % 2)
        
        layout.addLayout(grid)
        self.setLayout(layout)
    
    def create_stat_widget(self, title, value):
        frame = QFrame()
        frame.setFrameStyle(QFrame.Box | QFrame.Raised)
        frame.setStyleSheet("""
            QFrame {
                background-color: #2C2C2C;
                border: 2px solid #666;
                border-radius: 10px;
                padding: 20px;
                margin: 10px;
            }
        """)
        
        layout = QVBoxLayout(frame)
        
        title_label = QLabel(title)
        title_label.setStyleSheet("color: #CCC; font-size: 14px;")
        layout.addWidget(title_label)
        
        value_label = QLabel(value)
        value_label.setStyleSheet("color: #FFF; font-size: 24px; font-weight: bold;")
        layout.addWidget(value_label)
        
        return frame
