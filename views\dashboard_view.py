from PyQt5.QtWidgets import (QW<PERSON>t, QVBoxLayout, QHBoxLayout, QLabel, QGridLayout,
                           QFrame, QPushButton, QSplitter)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont

class DashboardView(QWidget):
    def __init__(self):
        super().__init__()
        self.init_ui()

        # Setup timer for real-time updates
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_data)
        self.timer.start(30000)  # Update every 30 seconds

    def init_ui(self):
        """Initialize the user interface."""
        main_layout = QVBoxLayout()
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(20, 20, 20, 20)

        # Header section
        header_layout = self.create_header()
        main_layout.addLayout(header_layout)

        # Statistics cards section
        stats_layout = self.create_stats_section()
        main_layout.addLayout(stats_layout)

        # Charts and activity section
        content_splitter = QSplitter(Qt.Horizontal)

        # Left side - Charts
        charts_widget = self.create_charts_section()
        content_splitter.addWidget(charts_widget)

        # Right side - Recent activity
        activity_widget = self.create_activity_section()
        content_splitter.addWidget(activity_widget)

        content_splitter.setSizes([700, 400])
        main_layout.addWidget(content_splitter)

        self.setLayout(main_layout)

    def create_header(self):
        """Create the header section with title and navigation."""
        header_layout = QHBoxLayout()

        # Title section
        title_frame = QFrame()
        title_layout = QHBoxLayout(title_frame)

        title_label = QLabel("TNB")
        title_label.setFont(QFont("Arial", 28, QFont.Bold))
        title_label.setStyleSheet("color: #2C3E50; margin-right: 10px;")

        subtitle_label = QLabel("Tableau de Bord")
        subtitle_label.setFont(QFont("Arial", 18))
        subtitle_label.setStyleSheet("color: #7F8C8D;")

        title_layout.addWidget(title_label)
        title_layout.addWidget(subtitle_label)
        title_layout.addStretch()

        header_layout.addWidget(title_frame)

        # Quick action buttons
        actions_frame = QFrame()
        actions_layout = QHBoxLayout(actions_frame)

        quick_buttons = [
            ("➕ Nouveau Redevable", "#27AE60"),
            ("💰 Nouveau Paiement", "#3498DB"),
            ("📊 Rapports", "#9B59B6"),
            ("🔄 Actualiser", "#E67E22")
        ]

        for text, color in quick_buttons:
            btn = QPushButton(text)
            btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 6px;
                    font-weight: bold;
                    font-size: 12px;
                }}
                QPushButton:hover {{
                    background-color: {self.darken_color(color)};
                }}
            """)
            actions_layout.addWidget(btn)

        header_layout.addWidget(actions_frame)

        return header_layout

    def create_stats_section(self):
        """Create the statistics cards section."""
        stats_layout = QGridLayout()
        stats_layout.setSpacing(15)

        # Statistics data with icons and trends
        stats_data = [
            ("👤 Redevables", "0", "+12% depuis le mois dernier", "#3498DB"),
            ("🏞️ Terrains", "0", "+8% depuis le mois dernier", "#27AE60"),
            ("💰 Paiements", "0", "+4% depuis le mois dernier", "#E74C3C"),
            ("💵 Montant Total", "0 DH", "+6% depuis le mois dernier", "#9B59B6")
        ]

        for i, (title, value, trend, color) in enumerate(stats_data):
            card = self.create_stat_card(title, value, trend, color)
            stats_layout.addWidget(card, 0, i)

        return stats_layout

    def create_stat_card(self, title, value, trend, color):
        """Create a modern statistics card."""
        card = QFrame()
        card.setFrameStyle(QFrame.Box)
        card.setStyleSheet(f"""
            QFrame {{
                background-color: white;
                border: 1px solid #E0E0E0;
                border-radius: 12px;
                padding: 20px;
                margin: 5px;
            }}
            QFrame:hover {{
                border: 2px solid {color};
                box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            }}
        """)

        layout = QVBoxLayout(card)
        layout.setSpacing(10)

        # Title with icon
        title_label = QLabel(title)
        title_label.setFont(QFont("Arial", 14, QFont.Bold))
        title_label.setStyleSheet(f"color: {color}; margin-bottom: 5px;")
        layout.addWidget(title_label)

        # Value
        value_label = QLabel(value)
        value_label.setFont(QFont("Arial", 28, QFont.Bold))
        value_label.setStyleSheet("color: #2C3E50; margin: 10px 0;")
        layout.addWidget(value_label)

        # Trend indicator
        trend_label = QLabel(trend)
        trend_label.setFont(QFont("Arial", 10))
        trend_label.setStyleSheet("color: #27AE60; font-style: italic;")
        layout.addWidget(trend_label)

        # Store reference for updates
        setattr(self, f"{title.split()[1].lower()}_value", value_label)

        return card

    def create_charts_section(self):
        """Create the charts section."""
        charts_widget = QFrame()
        charts_layout = QVBoxLayout(charts_widget)
        charts_layout.setSpacing(15)

        # Monthly revenue chart
        revenue_frame = self.create_chart_frame("📈 Revenus Mensuels")
        charts_layout.addWidget(revenue_frame)

        # Payment sources chart
        sources_frame = self.create_chart_frame("🥧 Sources de Paiement")
        charts_layout.addWidget(sources_frame)

        return charts_widget

    def create_activity_section(self):
        """Create the recent activity section."""
        activity_widget = QFrame()
        activity_layout = QVBoxLayout(activity_widget)

        # Header
        header_label = QLabel("🕒 Activité Récente")
        header_label.setFont(QFont("Arial", 16, QFont.Bold))
        header_label.setStyleSheet("color: #2C3E50; margin-bottom: 10px;")
        activity_layout.addWidget(header_label)

        # Activity list
        activity_frame = QFrame()
        activity_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #E0E0E0;
                border-radius: 8px;
                padding: 15px;
            }
        """)

        activity_list_layout = QVBoxLayout(activity_frame)

        # Sample activities
        activities = [
            ("Mohammed Alami", "Paiement effectué", "2 min", "✅"),
            ("Fatima Benani", "Nouveau compte créé", "15 min", "🆕"),
            ("Ahmed Tazi", "Mise à jour profil", "1h", "✏️"),
            ("Samira Idrissi", "Demande support", "3h", "❓"),
            ("Karim Berrada", "Paiement effectué", "5h", "✅")
        ]

        for user, action, time, status in activities:
            activity_item = self.create_activity_item(user, action, time, status)
            activity_list_layout.addWidget(activity_item)

        activity_layout.addWidget(activity_frame)

        return activity_widget

    def create_chart_frame(self, title):
        """Create a frame for charts."""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #E0E0E0;
                border-radius: 8px;
                padding: 15px;
                margin: 5px;
            }
        """)

        layout = QVBoxLayout(frame)

        # Title
        title_label = QLabel(title)
        title_label.setFont(QFont("Arial", 14, QFont.Bold))
        title_label.setStyleSheet("color: #2C3E50; margin-bottom: 10px;")
        layout.addWidget(title_label)

        # Placeholder for chart
        chart_placeholder = QLabel("📊 Graphique à implémenter")
        chart_placeholder.setAlignment(Qt.AlignCenter)
        chart_placeholder.setStyleSheet("""
            QLabel {
                background-color: #F8F9FA;
                border: 2px dashed #DEE2E6;
                border-radius: 8px;
                padding: 40px;
                color: #6C757D;
                font-size: 16px;
            }
        """)
        layout.addWidget(chart_placeholder)

        return frame

    def create_activity_item(self, user, action, time, status):
        """Create an activity item."""
        item_frame = QFrame()
        item_frame.setStyleSheet("""
            QFrame {
                background-color: #F8F9FA;
                border-radius: 6px;
                padding: 10px;
                margin: 2px 0;
            }
            QFrame:hover {
                background-color: #E9ECEF;
            }
        """)

        layout = QHBoxLayout(item_frame)
        layout.setContentsMargins(10, 5, 10, 5)

        # Status icon
        status_label = QLabel(status)
        status_label.setFont(QFont("Arial", 16))
        layout.addWidget(status_label)

        # User and action
        info_layout = QVBoxLayout()

        user_label = QLabel(user)
        user_label.setFont(QFont("Arial", 12, QFont.Bold))
        user_label.setStyleSheet("color: #2C3E50;")

        action_label = QLabel(action)
        action_label.setFont(QFont("Arial", 10))
        action_label.setStyleSheet("color: #6C757D;")

        info_layout.addWidget(user_label)
        info_layout.addWidget(action_label)
        layout.addLayout(info_layout)

        layout.addStretch()

        # Time
        time_label = QLabel(time)
        time_label.setFont(QFont("Arial", 10))
        time_label.setStyleSheet("color: #6C757D;")
        layout.addWidget(time_label)

        return item_frame

    def darken_color(self, color):
        """Darken a hex color for hover effects."""
        # Simple color darkening
        color_map = {
            "#27AE60": "#229954",
            "#3498DB": "#2E86C1",
            "#9B59B6": "#8E44AD",
            "#E67E22": "#D35400"
        }
        return color_map.get(color, color)

    def update_data(self):
        """Update dashboard data (placeholder for real implementation)."""
        # This would fetch real data from database
        # For now, just update with sample data
        if hasattr(self, 'redevables_value'):
            self.redevables_value.setText("42")
        if hasattr(self, 'terrains_value'):
            self.terrains_value.setText("78")
        if hasattr(self, 'paiements_value'):
            self.paiements_value.setText("156")
        if hasattr(self, 'montant_value'):
            self.montant_value.setText("234,567 DH")
