#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Payment Order Form for TNB Management System
"""

import tkinter as tk
from tkinter import messagebox
from datetime import datetime

class PaymentOrderDialog:
    def __init__(self, parent, tnb_form):
        """Initialize the payment order dialog."""
        self.parent = parent
        self.tnb_form = tnb_form
        self.window = None
        self.create_window()

    def create_window(self):
        """Create the Payment Order window."""
        self.window = tk.Toplevel(self.parent)
        self.window.title("Ordre de Versement T.N.B")
        self.window.geometry("1200x800")
        self.window.configure(bg="#2C3E50")
        self.window.transient(self.parent)
        self.window.grab_set()

        # Main container
        main_frame = tk.Frame(self.window, bg="#2C3E50")
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Create header
        self.create_header(main_frame)

        # Create form sections
        self.create_form_sections(main_frame)

        # Create payment table
        self.create_payment_table(main_frame)

        # Create bottom sections
        self.create_bottom_sections(main_frame)

        # Load data from TNB form
        self.load_data_from_tnb()

    def create_header(self, parent):
        """Create the header with title and buttons."""
        header_frame = tk.Frame(parent, bg="#2C3E50")
        header_frame.pack(fill=tk.X, pady=(0, 10))

        # Title
        title_label = tk.Label(
            header_frame,
            text="Ordre de Versement T.N.B",
            bg="#2C3E50",
            fg="#FFD700",
            font=("Arial", 20, "bold")
        )
        title_label.pack(side=tk.LEFT)

        # Buttons frame
        buttons_frame = tk.Frame(header_frame, bg="#2C3E50")
        buttons_frame.pack(side=tk.RIGHT)

        # Top buttons
        buttons = [
            ("Quitter", "#E74C3C", self.close_window),
            ("Impression avec Règlement", "#8B4513", self.print_with_payment),
            ("Impression sans Règlement", "#32CD32", self.print_without_payment),
            ("Notification", "#9B59B6", self.show_notification),
            ("Calculette", "#F39C12", self.show_calculator)
        ]

        for text, color, command in buttons:
            btn = tk.Button(
                buttons_frame,
                text=text,
                bg=color,
                fg="white",
                font=("Arial", 8, "bold"),
                command=command,
                padx=5,
                pady=2
            )
            btn.pack(side=tk.LEFT, padx=2)

    def create_form_sections(self, parent):
        """Create the form input sections."""
        form_frame = tk.Frame(parent, bg="white", relief=tk.RAISED, bd=2)
        form_frame.pack(fill=tk.X, pady=(0, 10))

        # Top section with taxpayer info
        top_section = tk.Frame(form_frame, bg="white")
        top_section.pack(fill=tk.X, padx=10, pady=5)

        # Redevables section
        redevables_frame = tk.Frame(top_section, bg="white")
        redevables_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        tk.Label(redevables_frame, text="Redevables", bg="white", font=("Arial", 10, "bold")).pack(anchor="w")
        self.redevables_entry = tk.Entry(redevables_frame, font=("Arial", 10), width=40)
        self.redevables_entry.pack(fill=tk.X, pady=2)

        # CIN section
        cin_frame = tk.Frame(top_section, bg="white")
        cin_frame.pack(side=tk.RIGHT, padx=(10, 0))

        tk.Label(cin_frame, text="Cin", bg="white", font=("Arial", 10, "bold")).pack(anchor="w")
        self.cin_entry = tk.Entry(cin_frame, font=("Arial", 10), width=15)
        self.cin_entry.pack(pady=2)

        # Second row
        second_row = tk.Frame(form_frame, bg="white")
        second_row.pack(fill=tk.X, padx=10, pady=5)

        # Order number and date
        order_frame = tk.Frame(second_row, bg="white")
        order_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        tk.Label(order_frame, text="N° d'Ordre de Versement", bg="white", font=("Arial", 10, "bold")).pack(anchor="w")
        self.order_number_entry = tk.Entry(order_frame, font=("Arial", 10), width=30)
        self.order_number_entry.pack(fill=tk.X, pady=2)

        tk.Label(order_frame, text="Date", bg="white", font=("Arial", 10, "bold")).pack(anchor="w")
        self.date_entry = tk.Entry(order_frame, font=("Arial", 10), width=30)
        self.date_entry.pack(fill=tk.X, pady=2)

        # RC and Zone
        rc_zone_frame = tk.Frame(second_row, bg="white")
        rc_zone_frame.pack(side=tk.RIGHT, padx=(10, 0))

        tk.Label(rc_zone_frame, text="N° RC", bg="white", font=("Arial", 10, "bold")).pack(anchor="w")
        self.rc_entry = tk.Entry(rc_zone_frame, font=("Arial", 10), width=15)
        self.rc_entry.pack(pady=2)

        tk.Label(rc_zone_frame, text="Zone", bg="white", font=("Arial", 10, "bold")).pack(anchor="w")
        self.zone_entry = tk.Entry(rc_zone_frame, font=("Arial", 10), width=15)
        self.zone_entry.pack(pady=2)

        # Third row with more details
        third_row = tk.Frame(form_frame, bg="white")
        third_row.pack(fill=tk.X, padx=10, pady=5)

        # Left side
        left_details = tk.Frame(third_row, bg="white")
        left_details.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # Designation
        tk.Label(left_details, text="Désignation", bg="white", font=("Arial", 10, "bold")).pack(anchor="w")
        self.designation_entry = tk.Entry(left_details, font=("Arial", 10), width=40)
        self.designation_entry.pack(fill=tk.X, pady=2)

        # Lot details
        lot_frame = tk.Frame(left_details, bg="white")
        lot_frame.pack(fill=tk.X, pady=2)

        tk.Label(lot_frame, text="N° Lot", bg="white", font=("Arial", 10, "bold")).pack(side=tk.LEFT)
        self.lot_entry = tk.Entry(lot_frame, font=("Arial", 10), width=10)
        self.lot_entry.pack(side=tk.LEFT, padx=(5, 10))

        tk.Label(lot_frame, text="Titre Foncier", bg="white", font=("Arial", 10, "bold")).pack(side=tk.LEFT)
        self.titre_entry = tk.Entry(lot_frame, font=("Arial", 10), width=15)
        self.titre_entry.pack(side=tk.LEFT, padx=(5, 10))

        tk.Label(lot_frame, text="Sup.", bg="white", font=("Arial", 10, "bold")).pack(side=tk.LEFT)
        self.sup_entry = tk.Entry(lot_frame, font=("Arial", 10), width=8)
        self.sup_entry.pack(side=tk.LEFT, padx=(5, 5))

        tk.Label(lot_frame, text="m²", bg="white", font=("Arial", 10)).pack(side=tk.LEFT)

        tk.Label(lot_frame, text="Tarif", bg="white", font=("Arial", 10, "bold")).pack(side=tk.LEFT, padx=(10, 0))
        self.tarif_entry = tk.Entry(lot_frame, font=("Arial", 10), width=8)
        self.tarif_entry.pack(side=tk.LEFT, padx=(5, 5))

        tk.Label(lot_frame, text="DH", bg="white", font=("Arial", 10)).pack(side=tk.LEFT)

        # Dates section
        dates_frame = tk.Frame(left_details, bg="white")
        dates_frame.pack(fill=tk.X, pady=2)

        tk.Label(dates_frame, text="Date d'Acquisit°", bg="white", font=("Arial", 10, "bold")).pack(side=tk.LEFT)
        self.date_acquisition_entry = tk.Entry(dates_frame, font=("Arial", 10), width=12)
        self.date_acquisition_entry.pack(side=tk.LEFT, padx=(5, 10))

        tk.Label(dates_frame, text="Date Permis", bg="white", font=("Arial", 10, "bold")).pack(side=tk.LEFT)
        self.date_permis_entry = tk.Entry(dates_frame, font=("Arial", 10), width=12)
        self.date_permis_entry.pack(side=tk.LEFT, padx=(5, 10))

        tk.Label(dates_frame, text="Date Autorisât°", bg="white", font=("Arial", 10, "bold")).pack(side=tk.LEFT)
        self.date_autorisation_entry = tk.Entry(dates_frame, font=("Arial", 10), width=12)
        self.date_autorisation_entry.pack(side=tk.LEFT, padx=(5, 0))

    def create_payment_table(self, parent):
        """Create the payment table."""
        table_frame = tk.Frame(parent, bg="white", relief=tk.RAISED, bd=2)
        table_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # Table headers
        headers = [
            "Année", "Superficie\nm²", "Tarif\nDH / m²", "Montant\nPrincipal",
            "Majoration\n15 %", "Majoration\n10 %", "Majoration\n5 %",
            "Nbr\nde\nMois", "Majoration\n0.5 %", "Montant\nà payer"
        ]

        # Create header row
        header_frame = tk.Frame(table_frame, bg="#4A90E2")
        header_frame.pack(fill=tk.X)

        for i, header in enumerate(headers):
            label = tk.Label(
                header_frame,
                text=header,
                bg="#4A90E2",
                fg="white",
                font=("Arial", 9, "bold"),
                relief=tk.RIDGE,
                bd=1,
                width=10,
                height=3
            )
            label.grid(row=0, column=i, sticky="ew")

        # Data area
        data_frame = tk.Frame(table_frame, bg="white")
        data_frame.pack(fill=tk.BOTH, expand=True)

        # Create payment entries grid
        self.payment_entries = []
        for row in range(8):
            row_entries = []
            for col in range(len(headers)):
                entry = tk.Entry(
                    data_frame,
                    width=10,
                    font=("Arial", 9),
                    justify=tk.CENTER,
                    relief=tk.RIDGE,
                    bd=1
                )
                entry.grid(row=row, column=col, sticky="ew", padx=1, pady=1)
                row_entries.append(entry)
            self.payment_entries.append(row_entries)

        # Total row
        total_frame = tk.Frame(table_frame, bg="#FFD700")
        total_frame.pack(fill=tk.X)

        tk.Label(total_frame, text="TOTAL", bg="#FFD700", font=("Arial", 10, "bold"), width=10).grid(row=0, column=0, sticky="ew")

        self.total_entries = []
        for col in range(1, len(headers)):
            entry = tk.Entry(
                total_frame,
                width=10,
                font=("Arial", 9, "bold"),
                justify=tk.CENTER,
                relief=tk.RIDGE,
                bd=1,
                bg="#FFD700"
            )
            entry.grid(row=0, column=col, sticky="ew", padx=1, pady=1)
            self.total_entries.append(entry)

    def create_bottom_sections(self, parent):
        """Create bottom sections with history and total."""
        bottom_frame = tk.Frame(parent, bg="#2C3E50")
        bottom_frame.pack(fill=tk.X)

        # Left side - History table
        left_frame = tk.Frame(bottom_frame, bg="white", relief=tk.RAISED, bd=2)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))

        # History headers
        history_headers = ["Année", "Article", "N° OV", "Date OV", "N° Quitt", "Date Quitt", "Montant", "Observations"]

        history_header_frame = tk.Frame(left_frame, bg="#17A2B8")
        history_header_frame.pack(fill=tk.X)

        for i, header in enumerate(history_headers):
            label = tk.Label(
                history_header_frame,
                text=header,
                bg="#17A2B8",
                fg="white",
                font=("Arial", 8, "bold"),
                relief=tk.RIDGE,
                bd=1,
                width=8
            )
            label.grid(row=0, column=i, sticky="ew")

        # History data
        history_data_frame = tk.Frame(left_frame, bg="white")
        history_data_frame.pack(fill=tk.BOTH, expand=True)

        # Sample history data
        sample_history = [
            ("2023", "", "41", "28-04-2025", "", "", "1 860,80", "DN 8745"),
            ("2024", "301", "28-01-2025", "43", "28-01-2025", "", "1 796,00", ""),
            ("2025", "", "301", "28-01-2025", "43", "28-01-2025", "1 080,00", "")
        ]

        for row, data in enumerate(sample_history):
            for col, value in enumerate(data):
                entry = tk.Entry(
                    history_data_frame,
                    width=8,
                    font=("Arial", 8),
                    justify=tk.CENTER,
                    relief=tk.RIDGE,
                    bd=1
                )
                entry.grid(row=row, column=col, sticky="ew", padx=1, pady=1)
                entry.insert(0, value)

        # History total
        history_total_frame = tk.Frame(left_frame, bg="#FFD700")
        history_total_frame.pack(fill=tk.X)

        tk.Label(history_total_frame, text="TOTAL", bg="#FFD700", font=("Arial", 9, "bold")).grid(row=0, column=0, columnspan=6, sticky="ew")
        total_entry = tk.Entry(history_total_frame, width=10, font=("Arial", 9, "bold"), justify=tk.CENTER, bg="#FFD700")
        total_entry.grid(row=0, column=6, sticky="ew", padx=1, pady=1)
        total_entry.insert(0, "4 736,80")

        # Right side - Majoration info
        right_frame = tk.Frame(bottom_frame, bg="#8B4513", relief=tk.RAISED, bd=2)
        right_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(5, 0))

        # Majoration title
        tk.Label(
            right_frame,
            text="Majoration de Vente",
            bg="#8B4513",
            fg="white",
            font=("Arial", 12, "bold"),
            pady=10
        ).pack()

        # Montant display
        montant_frame = tk.Frame(right_frame, bg="#000000", relief=tk.SUNKEN, bd=3)
        montant_frame.pack(padx=20, pady=10)

        tk.Label(montant_frame, text="Montant", bg="#000000", fg="#00FF00", font=("Arial", 14, "bold")).pack()
        tk.Label(montant_frame, text="0,00", bg="#000000", fg="#00FF00", font=("Arial", 20, "bold")).pack()

    def load_data_from_tnb(self):
        """Load data from TNB form."""
        try:
            # Get current date
            current_date = datetime.now().strftime("%d-%m-%Y")
            self.date_entry.insert(0, current_date)

            # Set default order number
            self.order_number_entry.insert(0, "2")

            # Sample data - in real app, get from selected taxpayer
            self.redevables_entry.insert(0, "ILYAS ZIANI ET CONSORT")
            self.cin_entry.insert(0, "F445357")
            self.rc_entry.insert(0, "2")
            self.zone_entry.insert(0, "IMMEUBLE EQUIPE")
            self.designation_entry.insert(0, "LOTISSEMENT EL AMAL 2")
            self.lot_entry.insert(0, "69")
            self.titre_entry.insert(0, "5343741")
            self.sup_entry.insert(0, "108,00")
            self.tarif_entry.insert(0, "10,00")
            self.date_acquisition_entry.insert(0, "29-03-2023")

            # Load payment data from TNB form if available
            if hasattr(self.tnb_form, 'payment_entries'):
                self.copy_payment_data()

        except Exception as e:
            print(f"Error loading data: {e}")

    def copy_payment_data(self):
        """Copy payment data from TNB form."""
        try:
            # Copy data from TNB payment table to this payment table
            for i, tnb_row in enumerate(self.tnb_form.payment_entries):
                if i < len(self.payment_entries):
                    for j, tnb_entry in enumerate(tnb_row):
                        if j < len(self.payment_entries[i]):
                            value = tnb_entry.get()
                            if value:
                                self.payment_entries[i][j].insert(0, value)
        except Exception as e:
            print(f"Error copying payment data: {e}")

    def close_window(self):
        """Close the window."""
        self.window.destroy()

    def print_with_payment(self):
        """Print with payment details."""
        messagebox.showinfo("Impression", "🖨️ Impression avec règlement lancée!")

    def print_without_payment(self):
        """Print without payment details."""
        try:
            # Import PDF generator
            import sys
            import os
            sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            from utils.pdf_generator import TNBPDFGenerator

            # Collect data from form
            data = {
                'agent': 'RAHMANI HICHAM',
                'cin': self.cin_entry.get() or 'C163053',
                'rc': self.rc_entry.get() or '',
                'if': '',
                'patente': '',
                'name': self.redevables_entry.get() or 'FATIMA KHLIFI TAGHZOUTI',
                'date_acquisition': self.date_acquisition_entry.get() or '30-12-2022',
                'date_autorisation': self.date_autorisation_entry.get() or '',
                'address': 'NR 60 RUE LAHSSEN LYOUSSI SEFROU',
                'lot': self.lot_entry.get() or '',
                'titre_foncier': self.titre_entry.get() or '472953',
                'superficie': self.sup_entry.get() or '111.00',
                'zone': self.zone_entry.get() or 'IMMEUBLE NON EQUIPE',
                'designation': self.designation_entry.get() or 'NR 02 LOTISSEMENT MOULAY ABDERRAHMANE EL ARAFI HABBOUNA',
                'rubrique': '417.***********',
                'quittance': '',
                'date_vente': '',
                'date_declaration': '',
                'quittance_num': '3162 - 3162',
                'date_quittance': '15-05-2025',
                'date_traitement': '15-05-2025',
                'jours_retard': '0',
                'majoration_vente': '',
                'superficie_globale': ''
            }

            # Generate PDF
            pdf_generator = TNBPDFGenerator()
            pdf_path = pdf_generator.generate_payment_order_without_payment(data)

            # Show success message and ask to open
            result = messagebox.askyesno(
                "Impression sans Règlement",
                f"📄 Document PDF généré avec succès!\n\n"
                f"Fichier: {os.path.basename(pdf_path)}\n\n"
                f"Voulez-vous ouvrir le document?"
            )

            if result:
                # Open PDF file
                import subprocess
                import platform

                if platform.system() == 'Windows':
                    os.startfile(pdf_path)
                elif platform.system() == 'Darwin':  # macOS
                    subprocess.call(['open', pdf_path])
                else:  # Linux
                    subprocess.call(['xdg-open', pdf_path])

        except ImportError as e:
            messagebox.showerror(
                "Erreur",
                f"❌ Module reportlab non installé!\n\n"
                f"Veuillez installer reportlab:\n"
                f"pip install reportlab\n\n"
                f"Erreur: {str(e)}"
            )
        except Exception as e:
            messagebox.showerror(
                "Erreur",
                f"❌ Erreur lors de la génération du PDF:\n\n{str(e)}"
            )

    def show_check_order(self):
        """Show check order."""
        messagebox.showinfo("Ordre de Recettes", "📋 Ordre de recettes par chèque")

    def show_notification(self):
        """Show notification."""
        messagebox.showinfo("Notification", "🔔 Notifications système")

    def show_calculator(self):
        """Show calculator."""
        messagebox.showinfo("Calculette", "🧮 Calculette T.N.B")
