#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Payment Order Form for TNB Management System
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import os
from datetime import datetime
from .styles import Styles

class PaymentOrderDialog(tk.Toplevel):
    def __init__(self, parent, app, callback=None):
        """Initialize the payment order dialog."""
        super().__init__(parent)
        self.parent = parent
        self.app = app
        self.callback = callback
        self.theme = app.theme

        # Configure window
        self.title("T.N.B Ordre de Versement")
        self.geometry("600x700")
        self.resizable(True, True)
        self.configure(bg=self.theme["bg"])

        # Center window
        self.center_window()

        # Create widgets
        self.create_widgets()

        # Make window modal
        self.transient(parent)
        self.grab_set()

        # Ensure window is visible and on top
        self.lift()
        self.attributes('-topmost', True)
        self.after_idle(self.attributes, '-topmost', False)

        # Force update to ensure window is shown
        self.update()

    def center_window(self):
        """Center the window on the screen."""
        self.update_idletasks()
        width = self.winfo_width()
        height = self.winfo_height()
        x = (self.winfo_screenwidth() // 2) - (width // 2)
        y = (self.winfo_screenheight() // 2) - (height // 2)
        self.geometry(f"{width}x{height}+{x}+{y}")

    def create_widgets(self):
        """Create the dialog widgets."""
        # Main frame with padding
        main_frame = tk.Frame(self, **Styles.get_frame_style(self.theme))
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # Title
        title_frame = tk.Frame(main_frame, bg=self.theme["bg"])
        title_frame.pack(fill=tk.X, pady=(0, 20))

        title_label = tk.Label(
            title_frame,
            text="ORDRE DE VERSEMENT T.N.B",
            **Styles.get_label_style(self.theme, size="title", weight="bold")
        )
        title_label.pack(anchor=tk.CENTER)

        subtitle_label = tk.Label(
            title_frame,
            text="Taxe sur les Terrains Non Bâtis",
            **Styles.get_label_style(self.theme, size="large")
        )
        subtitle_label.pack(anchor=tk.CENTER)

        # Create a notebook for different sections
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True, pady=10)

        # Taxpayer Information Tab
        taxpayer_frame = tk.Frame(notebook, bg=self.theme["bg"], padx=10, pady=10)
        notebook.add(taxpayer_frame, text="Information du Contribuable")

        # Property Information Tab
        property_frame = tk.Frame(notebook, bg=self.theme["bg"], padx=10, pady=10)
        notebook.add(property_frame, text="Information du Terrain")

        # Payment Information Tab
        payment_frame = tk.Frame(notebook, bg=self.theme["bg"], padx=10, pady=10)
        notebook.add(payment_frame, text="Information du Paiement")

        # Populate Taxpayer Tab
        self.create_taxpayer_section(taxpayer_frame)

        # Populate Property Tab
        self.create_property_section(property_frame)

        # Populate Payment Tab
        self.create_payment_section(payment_frame)

        # Buttons frame
        button_frame = tk.Frame(main_frame, bg=self.theme["bg"])
        button_frame.pack(fill=tk.X, pady=(20, 0))

        cancel_button = tk.Button(
            button_frame,
            text="Annuler",
            command=self.destroy,
            **Styles.get_button_style(self.theme, "danger")
        )
        cancel_button.pack(side=tk.LEFT, padx=5)

        preview_button = tk.Button(
            button_frame,
            text="Aperçu",
            command=self.preview_order,
            **Styles.get_button_style(self.theme, "info")
        )
        preview_button.pack(side=tk.LEFT, padx=5)

        save_button = tk.Button(
            button_frame,
            text="Générer l'Ordre",
            command=self.generate_order,
            **Styles.get_button_style(self.theme, "success")
        )
        save_button.pack(side=tk.RIGHT, padx=5)

    def create_taxpayer_section(self, parent):
        """Create the taxpayer information section."""
        # Taxpayer selection
        taxpayer_label = tk.Label(
            parent,
            text="Sélectionner un Contribuable:",
            **Styles.get_label_style(self.theme)
        )
        taxpayer_label.grid(row=0, column=0, sticky=tk.W, pady=5)

        self.taxpayer_var = tk.StringVar()
        self.taxpayer_combo = ttk.Combobox(
            parent,
            textvariable=self.taxpayer_var,
            state="readonly",
            width=30
        )
        self.taxpayer_combo.grid(row=0, column=1, sticky=tk.EW, pady=5, padx=5)
        self.taxpayer_combo.bind("<<ComboboxSelected>>", self.on_taxpayer_selected)

        # Load taxpayers
        self.load_taxpayers()

        # Or add new taxpayer
        add_button = tk.Button(
            parent,
            text="Nouveau",
            command=self.add_new_taxpayer,
            **Styles.get_button_style(self.theme)
        )
        add_button.grid(row=0, column=2, padx=5, pady=5)

        # Taxpayer details
        details_frame = tk.LabelFrame(
            parent,
            text="Détails du Contribuable",
            bg=self.theme["bg"],
            fg=self.theme["fg"],
            font=("Arial", 10, "bold"),
            padx=10,
            pady=10
        )
        details_frame.grid(row=1, column=0, columnspan=3, sticky=tk.NSEW, pady=10)

        # CIN
        cin_label = tk.Label(
            details_frame,
            text="CIN:",
            **Styles.get_label_style(self.theme)
        )
        cin_label.grid(row=0, column=0, sticky=tk.W, pady=5)

        self.cin_var = tk.StringVar()
        cin_entry = tk.Entry(
            details_frame,
            textvariable=self.cin_var,
            state="readonly",
            **Styles.get_entry_style(self.theme)
        )
        cin_entry.grid(row=0, column=1, sticky=tk.EW, pady=5, padx=5)

        # Fiscal ID
        fiscal_id_label = tk.Label(
            details_frame,
            text="Identifiant Fiscal:",
            **Styles.get_label_style(self.theme)
        )
        fiscal_id_label.grid(row=1, column=0, sticky=tk.W, pady=5)

        self.fiscal_id_var = tk.StringVar()
        fiscal_id_entry = tk.Entry(
            details_frame,
            textvariable=self.fiscal_id_var,
            state="readonly",
            **Styles.get_entry_style(self.theme)
        )
        fiscal_id_entry.grid(row=1, column=1, sticky=tk.EW, pady=5, padx=5)

        # Address
        address_label = tk.Label(
            details_frame,
            text="Adresse:",
            **Styles.get_label_style(self.theme)
        )
        address_label.grid(row=2, column=0, sticky=tk.W, pady=5)

        self.address_var = tk.StringVar()
        address_entry = tk.Entry(
            details_frame,
            textvariable=self.address_var,
            state="readonly",
            **Styles.get_entry_style(self.theme)
        )
        address_entry.grid(row=2, column=1, sticky=tk.EW, pady=5, padx=5)

        # Configure grid
        parent.grid_columnconfigure(1, weight=1)
        details_frame.grid_columnconfigure(1, weight=1)

    def create_property_section(self, parent):
        """Create the property information section."""
        # Property selection
        property_label = tk.Label(
            parent,
            text="Sélectionner un Terrain:",
            **Styles.get_label_style(self.theme)
        )
        property_label.grid(row=0, column=0, sticky=tk.W, pady=5)

        self.property_var = tk.StringVar()
        self.property_combo = ttk.Combobox(
            parent,
            textvariable=self.property_var,
            state="readonly",
            width=30
        )
        self.property_combo.grid(row=0, column=1, sticky=tk.EW, pady=5, padx=5)
        self.property_combo.bind("<<ComboboxSelected>>", self.on_property_selected)

        # Or add new property
        add_button = tk.Button(
            parent,
            text="Nouveau",
            command=self.add_new_property,
            **Styles.get_button_style(self.theme)
        )
        add_button.grid(row=0, column=2, padx=5, pady=5)

        # Property details
        details_frame = tk.LabelFrame(
            parent,
            text="Détails du Terrain",
            bg=self.theme["bg"],
            fg=self.theme["fg"],
            font=("Arial", 10, "bold"),
            padx=10,
            pady=10
        )
        details_frame.grid(row=1, column=0, columnspan=3, sticky=tk.NSEW, pady=10)

        # Reference
        ref_label = tk.Label(
            details_frame,
            text="Référence:",
            **Styles.get_label_style(self.theme)
        )
        ref_label.grid(row=0, column=0, sticky=tk.W, pady=5)

        self.ref_var = tk.StringVar()
        ref_entry = tk.Entry(
            details_frame,
            textvariable=self.ref_var,
            state="readonly",
            **Styles.get_entry_style(self.theme)
        )
        ref_entry.grid(row=0, column=1, sticky=tk.EW, pady=5, padx=5)

        # Address
        prop_address_label = tk.Label(
            details_frame,
            text="Adresse:",
            **Styles.get_label_style(self.theme)
        )
        prop_address_label.grid(row=1, column=0, sticky=tk.W, pady=5)

        self.prop_address_var = tk.StringVar()
        prop_address_entry = tk.Entry(
            details_frame,
            textvariable=self.prop_address_var,
            state="readonly",
            **Styles.get_entry_style(self.theme)
        )
        prop_address_entry.grid(row=1, column=1, sticky=tk.EW, pady=5, padx=5)

        # Area
        area_label = tk.Label(
            details_frame,
            text="Superficie (m²):",
            **Styles.get_label_style(self.theme)
        )
        area_label.grid(row=2, column=0, sticky=tk.W, pady=5)

        self.area_var = tk.StringVar()
        area_entry = tk.Entry(
            details_frame,
            textvariable=self.area_var,
            state="readonly",
            **Styles.get_entry_style(self.theme)
        )
        area_entry.grid(row=2, column=1, sticky=tk.EW, pady=5, padx=5)

        # Zone
        zone_label = tk.Label(
            details_frame,
            text="Zone:",
            **Styles.get_label_style(self.theme)
        )
        zone_label.grid(row=3, column=0, sticky=tk.W, pady=5)

        self.zone_var = tk.StringVar()
        zone_entry = tk.Entry(
            details_frame,
            textvariable=self.zone_var,
            state="readonly",
            **Styles.get_entry_style(self.theme)
        )
        zone_entry.grid(row=3, column=1, sticky=tk.EW, pady=5, padx=5)

        # Category
        category_label = tk.Label(
            details_frame,
            text="Catégorie:",
            **Styles.get_label_style(self.theme)
        )
        category_label.grid(row=4, column=0, sticky=tk.W, pady=5)

        self.category_var = tk.StringVar()
        category_entry = tk.Entry(
            details_frame,
            textvariable=self.category_var,
            state="readonly",
            **Styles.get_entry_style(self.theme)
        )
        category_entry.grid(row=4, column=1, sticky=tk.EW, pady=5, padx=5)

        # Configure grid
        parent.grid_columnconfigure(1, weight=1)
        details_frame.grid_columnconfigure(1, weight=1)

    def create_payment_section(self, parent):
        """Create the payment information section."""
        # Payment details
        details_frame = tk.LabelFrame(
            parent,
            text="Détails du Paiement",
            bg=self.theme["bg"],
            fg=self.theme["fg"],
            font=("Arial", 10, "bold"),
            padx=10,
            pady=10
        )
        details_frame.pack(fill=tk.BOTH, expand=True, pady=10)

        # Year
        year_label = tk.Label(
            details_frame,
            text="Année d'Imposition:",
            **Styles.get_label_style(self.theme)
        )
        year_label.grid(row=0, column=0, sticky=tk.W, pady=5)

        current_year = datetime.now().year
        years = [str(year) for year in range(current_year - 5, current_year + 2)]
        self.year_var = tk.StringVar(value=str(current_year))
        self.year_combo = ttk.Combobox(
            details_frame,
            textvariable=self.year_var,
            values=years,
            state="readonly",
            width=10
        )
        self.year_combo.grid(row=0, column=1, sticky=tk.W, pady=5, padx=5)

        # Amount
        amount_label = tk.Label(
            details_frame,
            text="Montant (DH):",
            **Styles.get_label_style(self.theme)
        )
        amount_label.grid(row=1, column=0, sticky=tk.W, pady=5)

        self.amount_entry = tk.Entry(
            details_frame,
            width=15,
            **Styles.get_entry_style(self.theme)
        )
        self.amount_entry.grid(row=1, column=1, sticky=tk.W, pady=5, padx=5)

        # Due date
        due_date_label = tk.Label(
            details_frame,
            text="Date d'Échéance:",
            **Styles.get_label_style(self.theme)
        )
        due_date_label.grid(row=2, column=0, sticky=tk.W, pady=5)

        due_date_frame = tk.Frame(details_frame, bg=self.theme["bg"])
        due_date_frame.grid(row=2, column=1, sticky=tk.W, pady=5, padx=5)

        # Day
        self.day_var = tk.StringVar(value=str(datetime.now().day))
        self.day_combo = ttk.Combobox(
            due_date_frame,
            textvariable=self.day_var,
            values=[str(i).zfill(2) for i in range(1, 32)],
            state="readonly",
            width=3
        )
        self.day_combo.pack(side=tk.LEFT, padx=2)

        # Month
        months = ["01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12"]
        self.month_var = tk.StringVar(value=str(datetime.now().month).zfill(2))
        self.month_combo = ttk.Combobox(
            due_date_frame,
            textvariable=self.month_var,
            values=months,
            state="readonly",
            width=3
        )
        self.month_combo.pack(side=tk.LEFT, padx=2)

        # Year
        self.due_year_var = tk.StringVar(value=str(datetime.now().year))
        self.due_year_combo = ttk.Combobox(
            due_date_frame,
            textvariable=self.due_year_var,
            values=[str(year) for year in range(current_year, current_year + 5)],
            state="readonly",
            width=5
        )
        self.due_year_combo.pack(side=tk.LEFT, padx=2)

        # Notes
        notes_label = tk.Label(
            details_frame,
            text="Notes:",
            **Styles.get_label_style(self.theme)
        )
        notes_label.grid(row=3, column=0, sticky=tk.NW, pady=5)

        self.notes_text = tk.Text(
            details_frame,
            height=5,
            width=30,
            **Styles.get_entry_style(self.theme)
        )
        self.notes_text.grid(row=3, column=1, sticky=tk.EW, pady=5, padx=5)

        # Configure grid
        details_frame.grid_columnconfigure(1, weight=1)

    def load_taxpayers(self):
        """Load taxpayers into the combobox."""
        try:
            taxpayers = self.app.db_manager.get_taxpayers()
            self.taxpayers = taxpayers

            taxpayer_options = []
            for taxpayer in taxpayers:
                taxpayer_options.append(f"{taxpayer['first_name']} {taxpayer['last_name']} ({taxpayer['cin']})")

            self.taxpayer_combo["values"] = taxpayer_options
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du chargement des contribuables: {str(e)}")

    def on_taxpayer_selected(self, event):
        """Handle taxpayer selection event."""
        if not self.taxpayer_var.get():
            return

        try:
            # Get selected taxpayer
            selected_index = self.taxpayer_combo.current()
            taxpayer_id = self.taxpayers[selected_index]["id"]
            taxpayer = self.taxpayers[selected_index]

            # Update taxpayer details
            self.cin_var.set(taxpayer["cin"])
            self.fiscal_id_var.set(taxpayer["fiscal_id"] or "")
            self.address_var.set(taxpayer["address"] or "")

            # Load properties for this taxpayer
            properties = self.app.db_manager.get_properties_by_taxpayer(taxpayer_id)
            self.properties = properties

            property_options = []
            for prop in properties:
                property_options.append(f"{prop['reference']} - {prop['address']}")

            self.property_combo["values"] = property_options

            if property_options:
                self.property_combo.current(0)
                self.on_property_selected(None)
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de la sélection du contribuable: {str(e)}")

    def on_property_selected(self, event):
        """Handle property selection event."""
        if not self.property_var.get():
            return

        try:
            # Get selected property
            selected_index = self.property_combo.current()
            property_data = self.properties[selected_index]

            # Update property details
            self.ref_var.set(property_data["reference"])
            self.prop_address_var.set(property_data["address"])
            self.area_var.set(str(property_data["area"]))
            self.zone_var.set(property_data["zone"])
            self.category_var.set(property_data["category"])

            # Calculate suggested amount based on property details
            # This is a placeholder - implement your actual calculation logic
            suggested_amount = property_data["area"] * 10  # Example: 10 DH per square meter
            self.amount_entry.delete(0, tk.END)
            self.amount_entry.insert(0, f"{suggested_amount:.2f}")
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de la sélection du terrain: {str(e)}")

    def add_new_taxpayer(self):
        """Show dialog to add a new taxpayer."""
        from .taxpayers import TaxpayerDialog
        dialog = TaxpayerDialog(self, self.app, callback=self.load_taxpayers)
        self.wait_window(dialog)

    def add_new_property(self):
        """Show dialog to add a new property."""
        # This would be implemented to show a property dialog
        messagebox.showinfo("Information", "Fonctionnalité à implémenter: Ajouter un nouveau terrain")

    def preview_order(self):
        """Preview the payment order."""
        # Validate inputs first
        if not self.validate_inputs():
            return

        # Get order data
        order_data = self.get_order_data()

        # Show a message that this would display a preview
        messagebox.showinfo("Aperçu", "Cette fonctionnalité afficherait un aperçu de l'ordre de versement avant génération.")

    def validate_inputs(self):
        """Validate the form inputs."""
        if not self.taxpayer_var.get():
            messagebox.showerror("Erreur", "Veuillez sélectionner un contribuable.")
            return False

        if not self.property_var.get():
            messagebox.showerror("Erreur", "Veuillez sélectionner un terrain.")
            return False

        try:
            amount = float(self.amount_entry.get().strip().replace(',', '.'))
            if amount <= 0:
                raise ValueError("Le montant doit être supérieur à zéro.")
        except ValueError:
            messagebox.showerror("Erreur", "Veuillez saisir un montant valide.")
            return False

        return True

    def get_order_data(self):
        """Get the data for the payment order."""
        selected_taxpayer_index = self.taxpayer_combo.current()
        taxpayer = self.taxpayers[selected_taxpayer_index]

        selected_property_index = self.property_combo.current()
        property_data = self.properties[selected_property_index]

        year = int(self.year_var.get())
        amount = float(self.amount_entry.get().strip().replace(',', '.'))
        due_date = f"{self.day_var.get()}/{self.month_var.get()}/{self.due_year_var.get()}"
        notes = self.notes_text.get("1.0", tk.END).strip()

        return {
            "taxpayer": taxpayer,
            "property": property_data,
            "payment": {
                "year": year,
                "amount": amount,
                "due_date": due_date,
                "notes": notes
            }
        }

    def generate_order(self):
        """Generate the payment order."""
        # Validate inputs
        if not self.validate_inputs():
            return

        try:
            # Get order data
            order_data = self.get_order_data()

            # Generate PDF
            pdf_path = self.app.pdf_generator.generate_payment_order(order_data)

            # Show success message
            messagebox.showinfo("Succès", f"Ordre de versement généré avec succès: {pdf_path}")

            # Ask if user wants to open the PDF
            if messagebox.askyesno("Ouvrir", "Voulez-vous ouvrir l'ordre de versement?"):
                os.startfile(pdf_path)

            # Close the dialog
            self.destroy()
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de la génération de l'ordre de versement: {str(e)}")
