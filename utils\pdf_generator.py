#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
PDF Generator for TNB Management System
"""

import os
from datetime import datetime
from reportlab.lib.pagesizes import A4
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import mm, cm
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.pdfgen import canvas
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT

class TNBPDFGenerator:
    def __init__(self):
        self.page_width, self.page_height = A4

    def generate_payment_order_without_payment(self, data):
        """Generate payment order PDF without payment details (Impression sans Règlement)."""
        # Create output directory if it doesn't exist
        output_dir = "output"
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        # Generate filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"etat_versement_{timestamp}.pdf"
        filepath = os.path.join(output_dir, filename)

        # Create PDF
        doc = SimpleDocTemplate(
            filepath,
            pagesize=A4,
            rightMargin=20*mm,
            leftMargin=20*mm,
            topMargin=15*mm,
            bottomMargin=15*mm
        )

        # Build content
        story = []

        # Header section
        self.add_header(story, data)

        # Main content
        self.add_main_content(story, data)

        # Payment table
        self.add_payment_table(story, data)

        # Footer
        self.add_footer(story)

        # Build PDF
        doc.build(story)

        return filepath

    def add_header(self, story, data):
        """Add header section to the PDF."""
        # Header table with government info and exercise info
        header_data = [
            [
                "ROYAUME DU MAROC\nMINISTERE DE L'INTERIEUR\nPROVINCE DE SETROU\nCOMMUNE DE SETROU\nV.F.R.H.B.C\nS.F.B.C.C / BUREAU D'ADMINISTRATION FISCALE",
                "",
                f"Exercice 2025\nArticle N° : 0007685"
            ]
        ]

        header_table = Table(header_data, colWidths=[8*cm, 4*cm, 6*cm])
        header_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (0, 0), 9),
            ('FONTSIZE', (2, 0), (2, 0), 10),
            ('ALIGN', (0, 0), (0, 0), 'LEFT'),
            ('ALIGN', (2, 0), (2, 0), 'RIGHT'),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
        ]))

        story.append(header_table)
        story.append(Spacer(1, 10*mm))

        # Title
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=getSampleStyleSheet()['Heading1'],
            fontSize=14,
            spaceAfter=10,
            alignment=TA_CENTER,
            textColor=colors.white,
            backColor=colors.black,
            borderPadding=5
        )

        title = Paragraph("ETAT DE VERSEMENT", title_style)
        story.append(title)
        story.append(Spacer(1, 5*mm))

    def add_main_content(self, story, data):
        """Add main content section."""
        # Agent info
        agent_data = [
            [f"Agent : {data.get('agent', 'RAHMANI HICHAM')}", "", "", ""]
        ]

        agent_table = Table(agent_data, colWidths=[18*cm])
        agent_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
        ]))

        story.append(agent_table)
        story.append(Spacer(1, 3*mm))

        # Main information table
        main_data = [
            [
                f"N° Cin",
                f"{data.get('cin', 'C163053')}",
                f"N° R.C.",
                f"{data.get('rc', '')}"
            ],
            [
                f"N° I.F",
                f"{data.get('if', '')}",
                f"N° Patente",
                f"{data.get('patente', '')}"
            ],
            [
                f"Mr (me)",
                f"{data.get('name', 'FATIMA KHLIFI TAGHZOUTI')}",
                "",
                ""
            ],
            [
                f"Adresse",
                f"{data.get('address', 'NR 60 RUE LAHSSEN LYOUSSI SEFROU')}",
                "",
                ""
            ],
            [
                f"N° Lot",
                f"{data.get('lot', '')}",
                f"Titre Foncier : NI",
                f"{data.get('titre_foncier', '472953')}"
            ]
        ]

        main_table = Table(main_data, colWidths=[3*cm, 6*cm, 3*cm, 6*cm])
        main_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 9),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('LEFTPADDING', (0, 0), (-1, -1), 3),
            ('RIGHTPADDING', (0, 0), (-1, -1), 3),
            ('TOPPADDING', (0, 0), (-1, -1), 3),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 3),
        ]))

        story.append(main_table)
        story.append(Spacer(1, 3*mm))

        # Additional info table
        additional_data = [
            [
                f"Date d'Acquisition",
                f"{data.get('date_acquisition', '30-12-2022')}",
                "",
                ""
            ],
            [
                f"Date d'Autorisation",
                f"{data.get('date_autorisation', '')}",
                "",
                ""
            ],
            [
                f"Superficie",
                f"{data.get('superficie', '111.00')} m²",
                f"Zone",
                f"{data.get('zone', 'IMMEUBLE NON EQUIPE')}"
            ]
        ]

        additional_table = Table(additional_data, colWidths=[3*cm, 6*cm, 3*cm, 6*cm])
        additional_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 9),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('LEFTPADDING', (0, 0), (-1, -1), 3),
            ('RIGHTPADDING', (0, 0), (-1, -1), 3),
            ('TOPPADDING', (0, 0), (-1, -1), 3),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 3),
        ]))

        story.append(additional_table)
        story.append(Spacer(1, 3*mm))

        # Designation section
        designation_text = f"Désignation : {data.get('designation', 'NR 02 LOTISSEMENT MOULAY ABDERRAHMANE EL ARAFI HABBOUNA')}"
        rubrique_text = f"Rubrique Budgétaire : {data.get('rubrique', '417.***********')}   Taxe sur les terrains urbains non bâtis"
        quittance_text = f"Quittance N° :"

        designation_data = [
            [designation_text],
            [rubrique_text],
            [quittance_text],
            [""],
            [f"Date de Vente :                    Date Déclaration de Vente :"]
        ]

        for row in designation_data:
            row_table = Table([row], colWidths=[18*cm])
            row_table.setStyle(TableStyle([
                ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 0), (-1, -1), 9),
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('LEFTPADDING', (0, 0), (-1, -1), 3),
            ]))
            story.append(row_table)

        story.append(Spacer(1, 5*mm))

        # Amount section with proper layout
        amount_header_data = [
            [
                f"Quittance N° : {data.get('quittance_num', '3162 - 3162')}",
                f"Date Quittance : {data.get('date_quittance', '15-05-2025')}",
                "Montant",
                f"Nbre Jrs de Retard : {data.get('jours_retard', '0')}"
            ]
        ]

        amount_main_data = [
            [
                "",
                "",
                "1 143,80",
                f"Majoration de Vente :"
            ]
        ]

        amount_footer_data = [
            [
                "",
                f"Date de Traitement : {data.get('date_traitement', '15-05-2025')}",
                "DH",
                f"Superficie Globale :     m²"
            ]
        ]

        # Create amount tables
        for table_data in [amount_header_data, amount_main_data, amount_footer_data]:
            amount_table = Table(table_data, colWidths=[4.5*cm, 4.5*cm, 4.5*cm, 4.5*cm])

            if table_data == amount_main_data:  # Main amount row
                amount_table.setStyle(TableStyle([
                    ('FONTNAME', (0, 0), (-1, -1), 'Helvetica-Bold'),
                    ('FONTSIZE', (2, 0), (2, 0), 16),  # Large amount
                    ('FONTSIZE', (0, 0), (1, 0), 9),
                    ('FONTSIZE', (3, 0), (3, 0), 9),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black),
                    ('ALIGN', (2, 0), (2, 0), 'CENTER'),
                    ('ALIGN', (0, 0), (1, 0), 'LEFT'),
                    ('ALIGN', (3, 0), (3, 0), 'LEFT'),
                    ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                    ('BACKGROUND', (2, 0), (2, 0), colors.black),
                    ('TEXTCOLOR', (2, 0), (2, 0), colors.white),
                    ('LEFTPADDING', (0, 0), (-1, -1), 3),
                    ('RIGHTPADDING', (0, 0), (-1, -1), 3),
                    ('TOPPADDING', (0, 0), (-1, -1), 3),
                    ('BOTTOMPADDING', (0, 0), (-1, -1), 3),
                ]))
            else:
                amount_table.setStyle(TableStyle([
                    ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
                    ('FONTSIZE', (0, 0), (-1, -1), 9),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black),
                    ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                    ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                    ('LEFTPADDING', (0, 0), (-1, -1), 3),
                    ('RIGHTPADDING', (0, 0), (-1, -1), 3),
                    ('TOPPADDING', (0, 0), (-1, -1), 3),
                    ('BOTTOMPADDING', (0, 0), (-1, -1), 3),
                ]))

            story.append(amount_table)

        story.append(Spacer(1, 5*mm))

    def add_payment_table(self, story, data):
        """Add payment details table."""
        # Table header
        header_data = [["Majorations"]]
        header_table = Table(header_data, colWidths=[18*cm])
        header_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 12),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('BACKGROUND', (0, 0), (-1, -1), colors.black),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.white),
            ('TOPPADDING', (0, 0), (-1, -1), 5),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 5),
        ]))

        story.append(header_table)

        # Payment table headers
        headers = [
            "Années", "Superficie\nm²", "Tarif\nDH / m²", "Montant\nPrincipal",
            "15 %", "10 %", "5 %", "Nbr de mois\nde retard", "0.5 %",
            "Total\nMajorations", "Montant\nà payer", "N°\nQuittance", "Date\nQuittance"
        ]

        # Sample payment data based on the image
        payment_data = [
            ["2025", "111.00", "5.00", "555.00", "500.00", "55.50", "27.75", "2", "5.55", "588.80", "1 143.80", "3162", "15/05/2025"]
        ]

        # Total row
        total_data = [
            ["TOTAL", "", "", "555.00", "500.00", "55.50", "27.75", "2", "5.55", "588.80", "1 143.80", "", ""]
        ]

        # Combine all table data
        table_data = [headers] + payment_data + total_data

        # Create table with appropriate column widths to fit A4
        col_widths = [1.3*cm, 1.3*cm, 1.3*cm, 1.5*cm, 1*cm, 1*cm, 1*cm, 1.3*cm, 1*cm, 1.5*cm, 1.5*cm, 1.2*cm, 1.5*cm]

        payment_table = Table(table_data, colWidths=col_widths)
        payment_table.setStyle(TableStyle([
            # Header styling
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 7),
            ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
            ('ALIGN', (0, 0), (-1, 0), 'CENTER'),

            # Data styling
            ('FONTNAME', (0, 1), (-1, -2), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -2), 8),
            ('ALIGN', (0, 1), (-1, -2), 'CENTER'),

            # Total row styling
            ('FONTNAME', (0, -1), (-1, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, -1), (-1, -1), 8),
            ('BACKGROUND', (0, -1), (-1, -1), colors.black),
            ('TEXTCOLOR', (0, -1), (-1, -1), colors.white),
            ('ALIGN', (0, -1), (-1, -1), 'CENTER'),

            # Grid
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('LEFTPADDING', (0, 0), (-1, -1), 1),
            ('RIGHTPADDING', (0, 0), (-1, -1), 1),
            ('TOPPADDING', (0, 0), (-1, -1), 2),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 2),
        ]))

        story.append(payment_table)

    def add_footer(self, story):
        """Add footer section."""
        story.append(Spacer(1, 10*mm))

        # Service signature
        footer_data = [
            ["", f"Le 15/05/2025"],
            ["", "SERVICE D'ASSIETTE"]
        ]

        footer_table = Table(footer_data, colWidths=[14*cm, 4*cm])
        footer_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('ALIGN', (1, 0), (1, -1), 'RIGHT'),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
        ]))

        story.append(footer_table)
