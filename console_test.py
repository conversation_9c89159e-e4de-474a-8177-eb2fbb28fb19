#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Console-based test for the TNB Management System
"""

import os
from database import DatabaseManager
from pdf_generator import PDFGenerator

def main():
    """Main function to test the application components."""
    print("Starting console test...")
    
    # Test database
    print("\nTesting database...")
    db_manager = DatabaseManager()
    db_manager.initialize_database()
    print("Database initialized successfully.")
    
    # Test user authentication
    print("\nTesting user authentication...")
    user = db_manager.authenticate_user("admin", "admin")
    if user:
        print(f"Authentication successful. User: {user['username']}, Role: {user['role']}")
    else:
        print("Authentication failed.")
    
    # Test adding a taxpayer
    print("\nTesting taxpayer management...")
    try:
        taxpayer_id = db_manager.add_taxpayer(
            "<PERSON>", "<PERSON><PERSON>", "AB123456", "123 Main St", "F12345", "0612345678", "<EMAIL>"
        )
        print(f"Taxpayer added successfully. ID: {taxpayer_id}")
        
        # Test getting taxpayers
        taxpayers = db_manager.get_taxpayers()
        print(f"Found {len(taxpayers)} taxpayers:")
        for taxpayer in taxpayers:
            print(f"  - {taxpayer['first_name']} {taxpayer['last_name']} (CIN: {taxpayer['cin']})")
        
        # Test adding a property
        property_id = db_manager.add_property(
            taxpayer_id, "PROP-001", "456 Property St", 1000, "Zone A", "Category 1"
        )
        print(f"Property added successfully. ID: {property_id}")
        
        # Test getting properties
        properties = db_manager.get_properties_by_taxpayer(taxpayer_id)
        print(f"Found {len(properties)} properties for taxpayer {taxpayer_id}:")
        for prop in properties:
            print(f"  - {prop['reference']} ({prop['address']}, {prop['area']} m²)")
        
        # Test adding a payment
        payment_id, receipt_number = db_manager.add_payment(
            taxpayer_id, property_id, 2023, 5000, "Test payment", 1
        )
        print(f"Payment added successfully. ID: {payment_id}, Receipt: {receipt_number}")
        
        # Test getting payments
        payments = db_manager.get_payments_by_taxpayer(taxpayer_id)
        print(f"Found {len(payments)} payments for taxpayer {taxpayer_id}:")
        for payment in payments:
            print(f"  - Year: {payment['year']}, Amount: {payment['amount']} DH, Receipt: {payment['receipt_number']}")
        
        # Test PDF generation
        print("\nTesting PDF generation...")
        pdf_generator = PDFGenerator()
        payment_data = db_manager.get_payment_by_id(payment_id)
        if payment_data:
            pdf_path = pdf_generator.generate_receipt(payment_data)
            print(f"PDF generated successfully: {pdf_path}")
            if os.path.exists(pdf_path):
                print(f"PDF file exists and is {os.path.getsize(pdf_path)} bytes.")
            else:
                print("PDF file does not exist.")
        else:
            print("Failed to get payment data for PDF generation.")
        
    except Exception as e:
        print(f"Error during testing: {str(e)}")
    
    print("\nConsole test completed.")

if __name__ == "__main__":
    main()
