#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Taxpayers Frame for TNB Management System
"""

import tkinter as tk
from tkinter import ttk, messagebox
from .styles import Styles

class TaxpayerDialog(tk.Toplevel):
    def __init__(self, parent, app, taxpayer=None, callback=None):
        """Initialize the taxpayer dialog."""
        super().__init__(parent)
        self.parent = parent
        self.app = app
        self.taxpayer = taxpayer
        self.callback = callback
        self.theme = app.theme

        # Configure window
        self.title("Ajouter un Redevable" if not taxpayer else "Modifier un Redevable")
        self.geometry("500x500")
        self.resizable(False, False)
        self.configure(bg=self.theme["bg"])

        # Center window
        self.center_window()

        # Create widgets
        self.create_widgets()

        # Make window modal
        self.transient(parent)
        self.grab_set()

    def center_window(self):
        """Center the window on the screen."""
        self.update_idletasks()
        width = self.winfo_width()
        height = self.winfo_height()
        x = (self.winfo_screenwidth() // 2) - (width // 2)
        y = (self.winfo_screenheight() // 2) - (height // 2)
        self.geometry(f"{width}x{height}+{x}+{y}")

    def create_widgets(self):
        """Create the dialog widgets."""
        # Main frame
        main_frame = tk.Frame(self, **Styles.get_frame_style(self.theme))
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # Title
        title_text = "Ajouter un Redevable" if not self.taxpayer else "Modifier un Redevable"
        title_label = tk.Label(
            main_frame,
            text=title_text,
            **Styles.get_label_style(self.theme, size="title", weight="bold")
        )
        title_label.pack(pady=(0, 20))

        # Form fields
        form_frame = tk.Frame(main_frame, bg=self.theme["bg"])
        form_frame.pack(fill=tk.BOTH, expand=True)

        # First name
        first_name_label = tk.Label(
            form_frame,
            text="Prénom:",
            **Styles.get_label_style(self.theme)
        )
        first_name_label.grid(row=0, column=0, sticky=tk.W, pady=5)

        self.first_name_entry = tk.Entry(
            form_frame,
            **Styles.get_entry_style(self.theme)
        )
        self.first_name_entry.grid(row=0, column=1, sticky=tk.EW, pady=5, padx=5)

        # Last name
        last_name_label = tk.Label(
            form_frame,
            text="Nom:",
            **Styles.get_label_style(self.theme)
        )
        last_name_label.grid(row=1, column=0, sticky=tk.W, pady=5)

        self.last_name_entry = tk.Entry(
            form_frame,
            **Styles.get_entry_style(self.theme)
        )
        self.last_name_entry.grid(row=1, column=1, sticky=tk.EW, pady=5, padx=5)

        # CIN
        cin_label = tk.Label(
            form_frame,
            text="CIN:",
            **Styles.get_label_style(self.theme)
        )
        cin_label.grid(row=2, column=0, sticky=tk.W, pady=5)

        self.cin_entry = tk.Entry(
            form_frame,
            **Styles.get_entry_style(self.theme)
        )
        self.cin_entry.grid(row=2, column=1, sticky=tk.EW, pady=5, padx=5)

        # Fiscal ID
        fiscal_id_label = tk.Label(
            form_frame,
            text="Identifiant Fiscal:",
            **Styles.get_label_style(self.theme)
        )
        fiscal_id_label.grid(row=3, column=0, sticky=tk.W, pady=5)

        self.fiscal_id_entry = tk.Entry(
            form_frame,
            **Styles.get_entry_style(self.theme)
        )
        self.fiscal_id_entry.grid(row=3, column=1, sticky=tk.EW, pady=5, padx=5)

        # Address
        address_label = tk.Label(
            form_frame,
            text="Adresse:",
            **Styles.get_label_style(self.theme)
        )
        address_label.grid(row=4, column=0, sticky=tk.W, pady=5)

        self.address_entry = tk.Entry(
            form_frame,
            **Styles.get_entry_style(self.theme)
        )
        self.address_entry.grid(row=4, column=1, sticky=tk.EW, pady=5, padx=5)

        # Phone
        phone_label = tk.Label(
            form_frame,
            text="Téléphone:",
            **Styles.get_label_style(self.theme)
        )
        phone_label.grid(row=5, column=0, sticky=tk.W, pady=5)

        self.phone_entry = tk.Entry(
            form_frame,
            **Styles.get_entry_style(self.theme)
        )
        self.phone_entry.grid(row=5, column=1, sticky=tk.EW, pady=5, padx=5)

        # Email
        email_label = tk.Label(
            form_frame,
            text="Email:",
            **Styles.get_label_style(self.theme)
        )
        email_label.grid(row=6, column=0, sticky=tk.W, pady=5)

        self.email_entry = tk.Entry(
            form_frame,
            **Styles.get_entry_style(self.theme)
        )
        self.email_entry.grid(row=6, column=1, sticky=tk.EW, pady=5, padx=5)

        # Configure grid
        form_frame.grid_columnconfigure(1, weight=1)

        # Buttons
        button_frame = tk.Frame(main_frame, bg=self.theme["bg"])
        button_frame.pack(fill=tk.X, pady=(20, 0))

        cancel_button = tk.Button(
            button_frame,
            text="Annuler",
            command=self.destroy,
            **Styles.get_button_style(self.theme, "danger")
        )
        cancel_button.pack(side=tk.LEFT, padx=5)

        save_button = tk.Button(
            button_frame,
            text="Enregistrer",
            command=self.save_taxpayer,
            **Styles.get_button_style(self.theme, "success")
        )
        save_button.pack(side=tk.RIGHT, padx=5)

        # If editing, populate fields
        if self.taxpayer:
            self.first_name_entry.insert(0, self.taxpayer["first_name"])
            self.last_name_entry.insert(0, self.taxpayer["last_name"])
            self.cin_entry.insert(0, self.taxpayer["cin"])
            if self.taxpayer["fiscal_id"]:
                self.fiscal_id_entry.insert(0, self.taxpayer["fiscal_id"])
            if self.taxpayer["address"]:
                self.address_entry.insert(0, self.taxpayer["address"])
            if self.taxpayer["phone"]:
                self.phone_entry.insert(0, self.taxpayer["phone"])
            if self.taxpayer["email"]:
                self.email_entry.insert(0, self.taxpayer["email"])

    def save_taxpayer(self):
        """Save the taxpayer data."""
        # Get values
        first_name = self.first_name_entry.get().strip()
        last_name = self.last_name_entry.get().strip()
        cin = self.cin_entry.get().strip()
        fiscal_id = self.fiscal_id_entry.get().strip() or None
        address = self.address_entry.get().strip() or None
        phone = self.phone_entry.get().strip() or None
        email = self.email_entry.get().strip() or None

        # Validate required fields
        if not first_name or not last_name or not cin:
            messagebox.showerror("Erreur", "Veuillez remplir tous les champs obligatoires (Prénom, Nom, CIN).")
            return

        try:
            if self.taxpayer:  # Update existing
                success = self.app.db_manager.update_taxpayer(
                    self.taxpayer["id"], first_name, last_name, cin, address, fiscal_id, phone, email
                )
                if success:
                    messagebox.showinfo("Succès", "Redevable mis à jour avec succès.")
                    if self.callback:
                        self.callback()
                    self.destroy()
            else:  # Add new
                taxpayer_id = self.app.db_manager.add_taxpayer(
                    first_name, last_name, cin, address, fiscal_id, phone, email
                )
                if taxpayer_id:
                    messagebox.showinfo("Succès", "Redevable ajouté avec succès.")
                    if self.callback:
                        self.callback()
                    self.destroy()
        except ValueError as e:
            messagebox.showerror("Erreur", str(e))


class TaxpayersFrame(tk.Frame):
    def __init__(self, parent, app):
        """Initialize the taxpayers frame."""
        self.app = app
        self.theme = app.theme

        super().__init__(parent, bg=self.theme["bg"])

        # Create widgets
        self.create_widgets()

    def create_widgets(self):
        """Create the taxpayers frame widgets."""
        # Main container with padding
        main_container = tk.Frame(self, bg=self.theme["bg"], padx=20, pady=20)
        main_container.pack(fill=tk.BOTH, expand=True)

        # Title and search bar
        top_frame = tk.Frame(main_container, bg=self.theme["bg"])
        top_frame.pack(fill=tk.X, pady=(0, 20))

        title_label = tk.Label(
            top_frame,
            text="Gestion des Redevables",
            **Styles.get_label_style(self.theme, size="title", weight="bold")
        )
        title_label.pack(side=tk.LEFT)

        # Search frame
        search_frame = tk.Frame(top_frame, bg=self.theme["bg"])
        search_frame.pack(side=tk.RIGHT)

        self.search_entry = tk.Entry(
            search_frame,
            width=30,
            **Styles.get_entry_style(self.theme)
        )
        self.search_entry.pack(side=tk.LEFT, padx=5)
        self.search_entry.insert(0, "Rechercher...")
        self.search_entry.bind("<FocusIn>", self.on_search_focus_in)
        self.search_entry.bind("<FocusOut>", self.on_search_focus_out)
        self.search_entry.bind("<Return>", lambda event: self.search_taxpayers())

        search_button = tk.Button(
            search_frame,
            text="Rechercher",
            command=self.search_taxpayers,
            **Styles.get_button_style(self.theme)
        )
        search_button.pack(side=tk.LEFT, padx=5)

        # Buttons frame
        buttons_frame = tk.Frame(main_container, bg=self.theme["bg"])
        buttons_frame.pack(fill=tk.X, pady=(0, 10))

        add_button = tk.Button(
            buttons_frame,
            text="Ajouter un Redevable",
            command=self.show_add_dialog,
            **Styles.get_button_style(self.theme, "success")
        )
        add_button.pack(side=tk.LEFT, padx=5)

        edit_button = tk.Button(
            buttons_frame,
            text="Modifier",
            command=self.show_edit_dialog,
            **Styles.get_button_style(self.theme)
        )
        edit_button.pack(side=tk.LEFT, padx=5)

        delete_button = tk.Button(
            buttons_frame,
            text="Supprimer",
            command=self.delete_taxpayer,
            **Styles.get_button_style(self.theme, "danger")
        )
        delete_button.pack(side=tk.LEFT, padx=5)

        view_button = tk.Button(
            buttons_frame,
            text="Voir les Détails",
            command=self.view_taxpayer_details,
            **Styles.get_button_style(self.theme, "info")
        )
        view_button.pack(side=tk.LEFT, padx=5)

        # Taxpayers treeview
        tree_frame = tk.Frame(main_container, bg=self.theme["bg"])
        tree_frame.pack(fill=tk.BOTH, expand=True)

        columns = ("id", "name", "cin", "fiscal_id", "address", "phone", "email")
        self.tree = ttk.Treeview(
            tree_frame,
            columns=columns,
            show="headings",
            selectmode="browse"
        )

        # Define headings
        self.tree.heading("id", text="ID")
        self.tree.heading("name", text="Nom Complet")
        self.tree.heading("cin", text="CIN")
        self.tree.heading("fiscal_id", text="ID Fiscal")
        self.tree.heading("address", text="Adresse")
        self.tree.heading("phone", text="Téléphone")
        self.tree.heading("email", text="Email")

        # Define columns
        self.tree.column("id", width=50, anchor=tk.CENTER)
        self.tree.column("name", width=200)
        self.tree.column("cin", width=100)
        self.tree.column("fiscal_id", width=100)
        self.tree.column("address", width=200)
        self.tree.column("phone", width=100)
        self.tree.column("email", width=150)

        # Add scrollbars
        y_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscroll=y_scrollbar.set)

        x_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.HORIZONTAL, command=self.tree.xview)
        self.tree.configure(xscroll=x_scrollbar.set)

        # Pack treeview and scrollbars
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        y_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        x_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)

        # Bind double-click event
        self.tree.bind("<Double-1>", lambda event: self.view_taxpayer_details())

        # Load taxpayers
        self.load_taxpayers()

    def on_search_focus_in(self, event):
        """Handle search entry focus in event."""
        if self.search_entry.get() == "Rechercher...":
            self.search_entry.delete(0, tk.END)

    def on_search_focus_out(self, event):
        """Handle search entry focus out event."""
        if not self.search_entry.get():
            self.search_entry.insert(0, "Rechercher...")

    def focus_search(self):
        """Set focus to the search entry."""
        self.search_entry.focus_set()
        if self.search_entry.get() == "Rechercher...":
            self.search_entry.delete(0, tk.END)

    def update_theme(self, theme):
        """Update the theme of all widgets."""
        self.theme = theme
        self.configure(bg=theme["bg"])

        # Update all child widgets (simplified for brevity)
        for widget in self.winfo_children():
            if isinstance(widget, tk.Frame):
                widget.configure(bg=theme["bg"])

    def load_taxpayers(self):
        """Load taxpayers from the database."""
        # Clear existing items
        for item in self.tree.get_children():
            self.tree.delete(item)

        # Get taxpayers from database
        taxpayers = self.app.db_manager.get_taxpayers()

        # Add to treeview
        for taxpayer in taxpayers:
            full_name = f"{taxpayer['first_name']} {taxpayer['last_name']}"
            self.tree.insert("", tk.END, values=(
                taxpayer["id"],
                full_name,
                taxpayer["cin"],
                taxpayer["fiscal_id"] or "",
                taxpayer["address"] or "",
                taxpayer["phone"] or "",
                taxpayer["email"] or ""
            ))

    def search_taxpayers(self):
        """Search taxpayers based on search entry."""
        search_term = self.search_entry.get()
        if search_term == "Rechercher...":
            search_term = ""

        # Clear existing items
        for item in self.tree.get_children():
            self.tree.delete(item)

        # Get taxpayers from database
        taxpayers = self.app.db_manager.get_taxpayers(search_term)

        # Add to treeview
        for taxpayer in taxpayers:
            full_name = f"{taxpayer['first_name']} {taxpayer['last_name']}"
            self.tree.insert("", tk.END, values=(
                taxpayer["id"],
                full_name,
                taxpayer["cin"],
                taxpayer["fiscal_id"] or "",
                taxpayer["address"] or "",
                taxpayer["phone"] or "",
                taxpayer["email"] or ""
            ))

    def show_add_dialog(self):
        """Show dialog to add a new taxpayer."""
        dialog = TaxpayerDialog(self, self.app, callback=self.load_taxpayers)
        self.wait_window(dialog)

    def show_edit_dialog(self):
        """Show dialog to edit the selected taxpayer."""
        selected_item = self.tree.selection()
        if not selected_item:
            messagebox.showwarning("Avertissement", "Veuillez sélectionner un redevable à modifier.")
            return

        taxpayer_id = self.tree.item(selected_item, "values")[0]
        taxpayer = self.app.db_manager.get_taxpayer_by_id(taxpayer_id)

        if taxpayer:
            dialog = TaxpayerDialog(self, self.app, taxpayer, callback=self.load_taxpayers)
            self.wait_window(dialog)

    def delete_taxpayer(self):
        """Delete the selected taxpayer."""
        selected_item = self.tree.selection()
        if not selected_item:
            messagebox.showwarning("Avertissement", "Veuillez sélectionner un redevable à supprimer.")
            return

        taxpayer_id = self.tree.item(selected_item, "values")[0]
        taxpayer_name = self.tree.item(selected_item, "values")[1]

        if messagebox.askyesno("Confirmation", f"Êtes-vous sûr de vouloir supprimer le redevable {taxpayer_name}?"):
            success = self.app.db_manager.delete_taxpayer(taxpayer_id)
            if success:
                messagebox.showinfo("Succès", "Redevable supprimé avec succès.")
                self.load_taxpayers()

    def view_taxpayer_details(self):
        """View details of the selected taxpayer."""
        selected_item = self.tree.selection()
        if not selected_item:
            messagebox.showwarning("Avertissement", "Veuillez sélectionner un redevable pour voir les détails.")
            return

        taxpayer_id = self.tree.item(selected_item, "values")[0]
        # This would navigate to a detailed view of the taxpayer
        # For now, we'll just show a message
        messagebox.showinfo("Détails", f"Affichage des détails pour le redevable ID: {taxpayer_id}")
        # In a real implementation, this would open a detailed view with properties and payments
